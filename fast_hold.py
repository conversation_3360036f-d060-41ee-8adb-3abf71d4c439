import json
import time
import logging
import hashlib
import asyncio
import random
from PyQt5.QtCore import QObject, pyqtSignal
import concurrent.futures
from urllib.parse import urljoin
from collections import deque
import queue
import statistics
from functools import lru_cache
import threading

from helper import get_http_client, make_request

from chart_token_manager import get_chart_token
from token_retrieval import get_hold_token as new_get_hold_token, get_cached_event_id

logger = logging.getLogger('webook_pro')

# Global connection pool and persistent clients
global_executor = concurrent.futures.ThreadPoolExecutor(
    max_workers=20,
    thread_name_prefix="FastHold"
)

# Pre-generated request templates
CACHED_BROWSER_IDS = [''.join(f"{random.randint(0, 0xFFFF):04x}" for _ in range(4)) for _ in range(50)]
CACHED_USER_AGENTS = [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.15',
    'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36'
]


@lru_cache(maxsize=1024)  # Increased cache size for better hit rate
def generate_x_signature_local(body: str) -> str:
    """Ultra-fast signature generation with optimizations"""
    try:
        # Get the current chart token - cached internally
        token = get_chart_token()
        if not token:
            # Fallback to empty token signature
            return hashlib.sha256(body.encode("utf-8")).hexdigest()
            
        # Reverse the token
        reversed_token = token[::-1]
        
        # Concatenate and hash in one go
        return hashlib.sha256((reversed_token + body).encode("utf-8")).hexdigest()
    except Exception:
        # Fast fallback without logging
        return hashlib.sha256(body.encode("utf-8")).hexdigest()

class FastHoldManager(QObject):
    """Ultra-optimized FastHoldManager for sub-40ms holds"""
    
    seat_held_signal = pyqtSignal(str, str)
    log_signal = pyqtSignal(str)
    performance_signal = pyqtSignal(dict)

    def __init__(self, event_key, chart_key, channel_keys, team_id, proxy=None, thread_pool_size=20, event_id=None):
        super().__init__()
        self.event_key = event_key
        self.chart_key = chart_key
        self.channel_keys = channel_keys
        self.team_id = team_id
        self.proxy = proxy
        self.token = None
        self.expire_time = None
        self.event_id = event_id
        
        self.metrics = {
            "response_times": deque(maxlen=100),
            "success_count": 0,
            "failure_count": 0
        }
        self.metrics_lock = threading.Lock()
        self.http_client = get_http_client(proxy=self.proxy) # Use the http.client wrapper

        self._get_initial_token()
        self._prepare_request_templates()
        
        self._loop = asyncio.new_event_loop()
        self._loop_thread = threading.Thread(
            target=self._run_event_loop,
            daemon=True,
            name="AsyncEventLoop"
        )
        self._loop_thread.start()

    def _run_event_loop(self):
        """Run asyncio event loop in background thread"""
        asyncio.set_event_loop(self._loop)
        self._loop.run_forever()



    def _build_optimized_channel_keys(self):
        """Ultra-fast channel key builder"""
        keys = []
        
        # Fast path for simple case
        if self.channel_keys == ['NO_CHANNEL'] or not self.channel_keys:
            return ['NO_CHANNEL']
            
        # Dictionary channel keys
        if isinstance(self.channel_keys, dict):
            # Add 'NO_CHANNEL' first for compatibility
            keys.append('NO_CHANNEL')
            
            # Add common key if available
            if 'common' in self.channel_keys and self.channel_keys['common']:
                keys.append(self.channel_keys['common'][0])
                
            # Add team-specific key if available
            if self.team_id and self.team_id in self.channel_keys and self.channel_keys[self.team_id]:
                keys.append(self.channel_keys[self.team_id][0])
            else:
                # Add first non-common key as fallback
                for key in self.channel_keys:
                    if key != 'common' and self.channel_keys[key]:
                        keys.append(self.channel_keys[key][0])
                        break
        
        # Ensure we have at least NO_CHANNEL
        if not keys:
            keys.append('NO_CHANNEL')
            
        return keys


    def _get_initial_token(self):
        """Get initial token using the new method"""
        try:
            # Get event ID from cache
            event_id = get_cached_event_id()
            if not event_id:
                self.log_signal.emit("No event ID available, waiting for event to load")
                return

            # Get new hold token
            hold_token = new_get_hold_token(event_id=event_id, proxy=self.proxy)
            if not hold_token:
                self.log_signal.emit("Failed to get initial token")
                return

            self.token = hold_token

            # Activate the token to get expiration time
            from helper import activate_hold_token
            time_left = activate_hold_token(hold_token, self.proxy)
            self.expire_time = time.time() + time_left

            self.log_signal.emit(f"Initial token {self.token[:8]}... activated")
        except Exception as e:
            self.log_signal.emit(f"Error getting initial token: {str(e)}")

    def _build_proxy_config(self):
        """Pre-build proxy configuration for HTTP requests"""
        if not self.proxy:
            return None
            
        parts = self.proxy.split(":")
        if len(parts) == 4:
            proxy_host, proxy_port, proxy_user, proxy_pass = parts
            return {
                "http://": f"http://{proxy_user}:{proxy_pass}@{proxy_host}:{proxy_port}",
                "https://": f"http://{proxy_user}:{proxy_pass}@{proxy_host}:{proxy_port}",
            }
        elif len(parts) == 2:
            proxy_host, proxy_port = parts
            return {
                "http://": f"http://{proxy_host}:{proxy_port}",
                "https://": f"http://{proxy_host}:{proxy_port}",
            }
        return None


    def _prepare_request_templates(self):
        """Pre-compute common request components for maximum performance"""
        self.hold_url = 'https://*************/system/public/3d443a0c-83b8-4a11-8c57-3db9d116ef76/events/groups/actions/hold-objects'
        self.optimized_keys = self._build_optimized_channel_keys()
        self.proxies = self._build_proxy_config()
        self.base_headers = {
            'accept': '*/*',
            'accept-language': 'en-US,en;q=0.9',
            'content-type': 'application/json',
            'origin': 'https://cdn-eu.seatsio.net',
            'host': 'cdn-eu.seatsio.net',  # Add Host header for IP connection
            'priority': 'u=1, i',
            'referer': 'https://cdn-eu.seatsio.net/static/version/seatsio-ui-prod-00221-fff/chart-renderer/chartRendererIframe.html?environment=PROD',
            'user-agent': CACHED_USER_AGENTS[0],
            'x-client-tool': 'Renderer',
            'x-kl-saas-ajax-request': 'Ajax_Request',
        }
        self.request_template = {
            'events': [self.event_key],
            'holdToken': None,
            'objects': [{'objectId': None}],
            'channelKeys': self.optimized_keys,
            'validateEventsLinkedToSameChart': True,
        }
    
    def hold_seat(self, seat_number):
        """Ultra-optimized seat holding with maximum performance"""
        global_executor.submit(self._hold_seat_optimized, seat_number)
        return True

    def _hold_seat_optimized(self, seat_number):
        """Optimized thread-based seat holder with minimal overhead"""
        asyncio.run_coroutine_threadsafe(
            self._hold_seat_async(seat_number),
            self._loop
        )

    async def _hold_seat_async(self, seat_number):
        """Ultra-fast asynchronous seat holding implementation using http.client"""
        start_time = time.time()
        success = False

        try:
            token = self.token
            if not token:
                self._get_initial_token()
                token = self.token
                if not token:
                    return False

            json_data = self.request_template.copy()
            json_data['holdToken'] = token
            json_data['objects'] = [{'objectId': seat_number}]
            
            x_sig = generate_x_signature_local(json.dumps(json_data, separators=(',', ':')))

            headers = self.base_headers.copy()
            headers['x-browser-id'] = random.choice(CACHED_BROWSER_IDS)
            headers['user-agent'] = random.choice(CACHED_USER_AGENTS)
            headers['x-signature'] = x_sig

            network_start_time = time.time()
            
            # Use the http.client wrapper from helper.py
            
            response = self.http_client.post(
                self.hold_url,
                json=json_data,
                headers=headers,
                timeout=1.0
            )

            network_elapsed_ms = (time.time() - network_start_time) * 1000
            
            if response.status_code == 204:
                success = True
                with self.metrics_lock:
                    self.metrics["response_times"].append(network_elapsed_ms)
                    self.metrics["success_count"] += 1
                
                self.performance_signal.emit({
                    'avg_ms': network_elapsed_ms,
                    'success': True,
                    'seat_id': seat_number,
                    'operation': 'hold'
                })
                
                logger.info(f"Held seat {seat_number} in {network_elapsed_ms:.2f}ms with token {token[:8]}...")
                self.seat_held_signal.emit(seat_number, self.token)
            else:
                with self.metrics_lock:
                    self.metrics["failure_count"] += 1
                
                self.performance_signal.emit({
                    'avg_ms': network_elapsed_ms,
                    'success': False,
                    'seat_id': seat_number,
                    'operation': 'hold'
                })
                logger.debug(f"Failed to hold seat {seat_number}: {response.status_code}")

        except Exception as e:
            with self.metrics_lock:
                self.metrics["failure_count"] += 1
            
            logger.debug(f"Error holding seat {seat_number}: {str(e)}")
            
            total_elapsed_ms = (time.time() - start_time) * 1000
            self.performance_signal.emit({
                'avg_ms': total_elapsed_ms,
                'success': False,
                'seat_id': seat_number,
                'operation': 'hold',
                'error': str(e)
            })
        
        return success

    def cleanup(self):
        """Clean up resources when done"""
        if self._loop and self._loop.is_running():
            self._loop.call_soon_threadsafe(self._loop.stop)

    def renew_token_if_needed(self):
        """Renew token if needed using the new method"""
        if self.token and self.expire_time and (self.expire_time - time.time() < 480):  # 8 minutes
            try:
                # Get event ID from cache
                event_id = get_cached_event_id()
                if not event_id:
                    self.log_signal.emit("No event ID available for token renewal")
                    return

                # Get new hold token
                hold_token = new_get_hold_token(event_id=event_id, proxy=self.proxy)
                if not hold_token:
                    self.log_signal.emit("Failed to renew token")
                    return

                self.token = hold_token

                # Activate the token
                from helper import activate_hold_token
                time_left = activate_hold_token(hold_token, self.proxy)
                self.expire_time = time.time() + time_left

                self.log_signal.emit(f"Token renewed: {self.token[:8]}...")
            except Exception as e:
                self.log_signal.emit(f"Error renewing token: {str(e)}")

@lru_cache(maxsize=1024)  # Increased cache size for better hit rate
def generate_x_signature_local(body: str) -> str:
    """Ultra-fast signature generation with optimizations"""
    try:
        # Get the current chart token - cached internally
        token = get_chart_token()
        if not token:
            # Fallback to empty token signature
            return hashlib.sha256(body.encode("utf-8")).hexdigest()
            
        # Reverse the token
        reversed_token = token[::-1]
        
        # Concatenate and hash in one go
        return hashlib.sha256((reversed_token + body).encode("utf-8")).hexdigest()
    except Exception:
        # Fast fallback without logging
        return hashlib.sha256(body.encode("utf-8")).hexdigest()
