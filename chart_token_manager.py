# chart_token_manager.py - Centralized management for chart tokens

import threading
import logging
import time
import re
import hashlib
import requests
from functools import lru_cache

logger = logging.getLogger('webook_pro')

# Direct token endpoint and regex pattern
TOKEN_ENDPOINT = 'https://cdn-eu.seatsio.net/chart.js'
TOKEN_REGEX = r"seatsio\.chartToken\s*=\s*'([a-fA-F0-9]+)'"

# Global shared token state
_chart_token = None
_chart_token_lock = threading.RLock()
_last_update_time = 0
_update_interval = 20  # seconds

# Persistent session
_session = None
_session_lock = threading.Lock()

def get_global_session():
    """Get or create a shared requests session with connection pooling"""
    global _session
    with _session_lock:
        if _session is None:
            _session = requests.Session()
            
            # Configure session for connection pooling
            adapter = requests.adapters.HTTPAdapter(
                pool_connections=20,
                pool_maxsize=50,
                max_retries=1
            )
            _session.mount('http://', adapter)
            _session.mount('https://', adapter)
    return _session

def fetch_chart_token_from_source(proxy=None):
    """Fetch chart token directly from the seatsio CDN (not from elitesoftworks)"""
    try:
        # Configure proxy if needed
        proxies = None
        if proxy:
            parts = proxy.split(":")
            if len(parts) == 4:
                proxy_host, proxy_port, proxy_user, proxy_pass = parts
                proxies = {
                    "http": f"socks5://{proxy_user}:{proxy_pass}@{proxy_host}:{proxy_port}",
                    "https": f"socks5://{proxy_user}:{proxy_pass}@{proxy_host}:{proxy_port}"
                }
            elif len(parts) == 2:
                proxy_host, proxy_port = parts
                proxies = {
                    "http": f"socks5://{proxy_host}:{proxy_port}",
                    "https": f"socks5://{proxy_host}:{proxy_port}"
                }
        
        # Fetch chart.js directly from seatsio CDN
        session = get_global_session()
        response = session.get(TOKEN_ENDPOINT, proxies=proxies, timeout=5)
        
        if response.status_code != 200:
            logger.error(f"Failed to fetch chart token: Status {response.status_code}")
            return None
            
        # Extract token using regex
        match = re.search(TOKEN_REGEX, response.text)
        if not match:
            logger.error("Failed to extract chart token from response")
            return None
            
        token = match.group(1)
        logger.info(f"Successfully fetched chart token: {token[:8]}...")
        return token
        
    except Exception as e:
        logger.error(f"Error fetching chart token: {str(e)}")
        return None

def get_chart_token(force_refresh=False, proxy=None):
    """Get current chart token, refreshing if needed"""
    global _chart_token, _last_update_time
    
    with _chart_token_lock:
        current_time = time.time()
        
        # Check if we need to refresh
        if force_refresh or _chart_token is None or (current_time - _last_update_time) > _update_interval:
            token = fetch_chart_token_from_source(proxy)
            if token:
                _chart_token = token
                _last_update_time = current_time
                logger.info(f"Chart token refreshed: {token[:8]}...")
            elif _chart_token is None:
                # If we still don't have a token, use a fallback (empty string)
                logger.warning("Using empty chart token as fallback")
                _chart_token = ""
        
        return _chart_token

@lru_cache(maxsize=128)
def generate_x_signature(body: str) -> str:
    """
    Generate X-Signature from body content using the managed chart token.
    This is a drop-in replacement for the elitesoftworks function.
    """
    token = get_chart_token()
    return _generate_signature(body, token)

def _generate_signature(body: str, token: str) -> str:
    """Internal function to generate signature from body and token"""
    try:
        if not token:
            return hashlib.sha256(body.encode("utf-8")).hexdigest()
            
        # Reverse the chart token (same as in the original function)
        reversed_token = token[::-1]
        
        # Concatenate reversed token + body
        data_to_hash = reversed_token + body
        
        # Compute SHA-256 hex digest
        return hashlib.sha256(data_to_hash.encode("utf-8")).hexdigest()
        
    except Exception as e:
        logger.error(f"Error generating signature: {str(e)}")
        # Fallback to empty signature
        return hashlib.sha256(body.encode("utf-8")).hexdigest()

# Start background token refresh timer
def _start_background_refresh():
    """Start a background thread to refresh the token periodically"""
    def refresh_loop():
        while True:
            try:
                # Get token with potential refresh
                get_chart_token()
                # Sleep until next check
                time.sleep(_update_interval / 2)
            except Exception as e:
                logger.error(f"Error in background token refresh: {str(e)}")
                time.sleep(5)  # Sleep a bit on error
    
    thread = threading.Thread(
        target=refresh_loop,
        daemon=True,
        name="ChartTokenRefresh"
    )
    thread.start()
    return thread

# Start background refresh
_refresh_thread = _start_background_refresh()