#!/usr/bin/env python3
# SeatSwitcherApp.py - Seat switching utility for Webook events

import sys
import os
import time
import json
import logging
import threading
from datetime import datetime

from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
    QLabel, QLineEdit, QPushButton, QComboBox, QFormLayout, 
    QTextEdit, QCheckBox, QMessageBox, QProgressBar, QGroupBox,
    QSplitter, QTableWidget, QTableWidgetItem, QHeaderView, QFileDialog
)
from PyQt5.QtCore import (
    Qt, pyqtSignal, QThread, QSettings, QTimer
)
from PyQt5.QtGui import QIcon, QFont

# Import helpers from existing system
from helper import (
    get_webook_event_info, 
    get_event_render_data,
    get_random_useragent,
    switch_seat_immediate
)
from proxy_config_dialog import ProxyConfigDialog

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='[%(levelname)s] %(asctime)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('seat_switcher.log', 'a', 'utf-8')
    ]
)
logger = logging.getLogger('seat_switcher')

# --------- Event Loading Worker ---------

class EventLoaderThread(QThread):
    """Thread for loading event data"""
    progress_signal = pyqtSignal(str, int)  # stage, percent
    result_signal = pyqtSignal(dict)  # event data
    error_signal = pyqtSignal(str)  # error message
    
    def __init__(self, event_key, proxy=None, parent=None):
        super().__init__(parent)
        self.event_key = event_key
        self.proxy = proxy
        
    def run(self):
        try:
            self.progress_signal.emit("Fetching event data", 10)
            
            # Use helper function to get webook event info
            response = get_webook_event_info(self.event_key)
            
            if not response.ok:
                self.error_signal.emit(f"Failed to load event: {response.status_code} - {response.reason}")
                return
                
            event_data = response.json()
            self.progress_signal.emit("Processing event data", 50)
            
            # Get additional seatsio data
            if event_data.get("data", {}).get("seats_io"):
                chart_key = event_data["data"]["seats_io"]["chart_key"]
                event_key = event_data["data"]["seats_io"]["event_key"]
                
                # Load seatsio rendering info
                self.progress_signal.emit("Loading seat data", 75)
                
                render_data = get_event_render_data(event_key)
                event_data["render_data"] = render_data
            
            self.progress_signal.emit("Complete", 100)
            self.result_signal.emit(event_data)
            
        except Exception as e:
            logger.exception("Error loading event")
            self.error_signal.emit(f"Error loading event: {str(e)}")

# --------- Seat Operation History Table ---------

class OperationHistoryTable(QTableWidget):
    """Table to display seat operation history"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setColumnCount(6)
        self.setHorizontalHeaderLabels(["Time", "Seat", "Old Token", "New Token", "Status", "Duration"])
        
        # Configure column widths
        header = self.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # Time
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # Seat
        header.setSectionResizeMode(2, QHeaderView.Stretch)  # Old Token
        header.setSectionResizeMode(3, QHeaderView.Stretch)  # New Token
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # Status
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # Duration
        
        self.setEditTriggers(QTableWidget.NoEditTriggers)
        self.setSelectionBehavior(QTableWidget.SelectRows)
        self.setAlternatingRowColors(True)
    
    def add_operation(self, operation_time, seat, old_token, new_token, status, duration=None):
        """Add a new operation to the history table"""
        row_position = self.rowCount()
        self.insertRow(row_position)
        
        # Format time
        time_str = operation_time.strftime("%H:%M:%S")
        
        # Create table items
        self.setItem(row_position, 0, QTableWidgetItem(time_str))
        self.setItem(row_position, 1, QTableWidgetItem(seat))
        self.setItem(row_position, 2, QTableWidgetItem(old_token))
        self.setItem(row_position, 3, QTableWidgetItem(new_token))
        
        status_item = QTableWidgetItem(status)
        if status == "Success":
            status_item.setForeground(Qt.darkGreen)
        elif status == "Failed":
            status_item.setForeground(Qt.red)
        else:
            status_item.setForeground(Qt.blue)
        self.setItem(row_position, 4, status_item)
        
        # Add duration if available
        if duration is not None:
            duration_str = f"{duration:.3f}s"
            self.setItem(row_position, 5, QTableWidgetItem(duration_str))
        else:
            self.setItem(row_position, 5, QTableWidgetItem(""))
            
        # Scroll to the new row
        self.scrollToBottom()
    
    def export_to_csv(self, filename):
        """Export operation history to CSV file"""
        try:
            with open(filename, 'w') as f:
                # Write header
                headers = []
                for i in range(self.columnCount()):
                    headers.append(self.horizontalHeaderItem(i).text())
                f.write(','.join(headers) + '\n')
                
                # Write data
                for row in range(self.rowCount()):
                    row_data = []
                    for col in range(self.columnCount()):
                        item = self.item(row, col)
                        if item is not None:
                            # Wrap text in quotes if it contains commas
                            text = item.text()
                            if ',' in text:
                                text = f'"{text}"'
                            row_data.append(text)
                        else:
                            row_data.append('')
                    f.write(','.join(row_data) + '\n')
                    
            return True
        except Exception as e:
            logger.error(f"Error exporting to CSV: {e}")
            return False

# --------- Main Application ---------

class SeatSwitcherApp(QMainWindow):
    """Main application window for seat switching"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Webook Seat Switcher")
        self.resize(1000, 700)
        
        # Initialize settings
        self.settings = QSettings("EliteSoftworks", "SeatSwitcher")
        
        # Initialize data structures
        self.event_data = None
        self.channel_keys = None
        self.teams = {}
        
        # Create the main widget and layout
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        
        main_layout = QVBoxLayout()
        self.central_widget.setLayout(main_layout)
        
        # Create splitter for top and bottom sections
        splitter = QSplitter(Qt.Vertical)
        main_layout.addWidget(splitter)
        
        # Create top widget (form controls)
        top_widget = QWidget()
        top_layout = QVBoxLayout()
        top_widget.setLayout(top_layout)
        
        # Create event loading section
        event_group = QGroupBox("Event Information")
        event_layout = QFormLayout()
        
        self.event_key_edit = QLineEdit()
        self.event_key_edit.setPlaceholderText("Enter event key or URL")
        
        self.load_button = QPushButton("Load Event")
        self.team_combo = QComboBox()
        
        event_input_layout = QHBoxLayout()
        event_input_layout.addWidget(self.event_key_edit)
        event_input_layout.addWidget(self.load_button)
        
        event_layout.addRow("Event Key:", event_input_layout)
        event_layout.addRow("Team:", self.team_combo)
        
        event_group.setLayout(event_layout)
        top_layout.addWidget(event_group)
        
        # Create seat switching section
        seat_group = QGroupBox("Seat Switching")
        seat_layout = QFormLayout()
        
        self.seat_edit = QLineEdit()
        self.old_token_edit = QLineEdit()
        self.new_token_edit = QLineEdit()
        
        self.switch_button = QPushButton("Switch Seat")
        self.switch_button.setEnabled(False)
        
        seat_layout.addRow("Seat Number:", self.seat_edit)
        seat_layout.addRow("Old Token:", self.old_token_edit)
        seat_layout.addRow("New Token:", self.new_token_edit)
        seat_layout.addRow("", self.switch_button)
        
        seat_group.setLayout(seat_layout)
        top_layout.addWidget(seat_group)
        
        # Create status and controls section
        status_group = QGroupBox("Status")
        status_layout = QVBoxLayout()
        
        self.status_bar = QProgressBar()
        self.status_bar.setRange(0, 100)
        self.status_bar.setValue(0)
        
        self.status_label = QLabel("Ready")
        
        control_layout = QHBoxLayout()
        self.configure_proxy_button = QPushButton("Configure Proxy")
        self.export_button = QPushButton("Export History")
        
        control_layout.addWidget(self.configure_proxy_button)
        control_layout.addWidget(self.export_button)
        
        status_layout.addWidget(self.status_bar)
        status_layout.addWidget(self.status_label)
        status_layout.addLayout(control_layout)
        
        status_group.setLayout(status_layout)
        top_layout.addWidget(status_group)
        
        # Create log output
        log_group = QGroupBox("Log")
        log_layout = QVBoxLayout()
        
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)
        
        log_group.setLayout(log_layout)
        top_layout.addWidget(log_group)
        
        # Add top widget to splitter
        splitter.addWidget(top_widget)
        
        # Create bottom widget (operation history)
        bottom_widget = QWidget()
        bottom_layout = QVBoxLayout()
        
        history_label = QLabel("Operation History")
        history_label.setAlignment(Qt.AlignCenter)
        history_label.setFont(QFont("Arial", 10, QFont.Bold))
        
        self.history_table = OperationHistoryTable()
        
        bottom_layout.addWidget(history_label)
        bottom_layout.addWidget(self.history_table)
        
        bottom_widget.setLayout(bottom_layout)
        
        # Add bottom widget to splitter
        splitter.addWidget(bottom_widget)
        
        # Set stretch factors for splitter
        splitter.setStretchFactor(0, 2)
        splitter.setStretchFactor(1, 1)
        
        # Connect signals
        self.load_button.clicked.connect(self.load_event)
        self.switch_button.clicked.connect(self.switch_seat)
        self.configure_proxy_button.clicked.connect(self.show_proxy_config)
        self.export_button.clicked.connect(self.export_history)
        
        # Load previous settings
        self.load_settings()
        
        # Log startup
        self.log("Seat Switcher started")
        
    def load_settings(self):
        """Load settings from QSettings"""
        event_key = self.settings.value("event/key", "")
        self.event_key_edit.setText(event_key)
        
        # Load proxy settings with consistent key names
        self.proxy_config = {
            "enabled": self.settings.value("proxy/enabled", False, type=bool),
            "domain": self.settings.value("proxy/domain", "p.webshare.io"),  # Changed from "host" to "domain"
            "port": self.settings.value("proxy/port", "80"),
            "username": self.settings.value("proxy/username", ""),
            "password": self.settings.value("proxy/password", "")
        }
        
        # Load window geometry if available
        if self.settings.contains("window/geometry"):
            self.restoreGeometry(self.settings.value("window/geometry"))
        
    def save_settings(self):
        """Save settings to QSettings"""
        self.settings.setValue("event/key", self.event_key_edit.text())
        
        # Save window geometry
        self.settings.setValue("window/geometry", self.saveGeometry())
        
    def closeEvent(self, event):
        """Handle window close event"""
        self.save_settings()
        super().closeEvent(event)
        
    def log(self, message):
        """Add a message to the log"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] {message}")
        # Ensure the latest message is visible
        self.log_text.ensureCursorVisible()
        # Also log to system logger
        logger.info(message)
        
    def get_proxy_string(self):
        """Get formatted proxy string from configuration"""
        if not self.proxy_config["enabled"]:
            return None
            
        domain = self.proxy_config["domain"]  # Changed from "host" to "domain"
        port = self.proxy_config["port"]
        username = self.proxy_config["username"]
        password = self.proxy_config["password"]
        
        if username and password:
            return f"{domain}:{port}:{username}:{password}"
        return f"{domain}:{port}"
            
    def show_proxy_config(self):
        """Show proxy configuration dialog"""
        dialog = ProxyConfigDialog(self)
        
        # Set initial values
        dialog.enable_checkbox.setChecked(self.proxy_config["enabled"])
        dialog.domain_edit.setText(self.proxy_config["domain"])  # Changed from "host" to "domain"
        dialog.port_edit.setText(self.proxy_config["port"])
        dialog.username_edit.setText(self.proxy_config["username"])
        dialog.password_edit.setText(self.proxy_config["password"])
        
        if dialog.exec_() == dialog.Accepted:
            # Get updated values
            settings = dialog.get_proxy_settings()
            self.proxy_config = settings
            
            # Save to settings
            self.settings.setValue("proxy/enabled", settings["enabled"])
            self.settings.setValue("proxy/domain", settings["domain"])  # Changed from "host" to "domain"
            self.settings.setValue("proxy/port", settings["port"])
            self.settings.setValue("proxy/username", settings["username"])
            self.settings.setValue("proxy/password", settings["password"])
            
            # Log change
            if settings["enabled"]:
                proxy_str = f"{settings['domain']}:{settings['port']}"  # Changed from "host" to "domain"
                if settings["username"]:
                    proxy_str += f" (user: {settings['username']})"
                self.log(f"Proxy enabled: {proxy_str}")
            else:
                self.log("Proxy disabled")
                
    def load_event(self):
        """Load event information"""
        event_key = self.event_key_edit.text().strip()
        if not event_key:
            QMessageBox.warning(self, "Warning", "Please enter an event key.")
            return
            
        # Extract event_key from URL if needed
        if '/' in event_key:
            import re
            found = re.search(r'events/(.*?)(?:/|$)', event_key)
            if found:
                event_key = found.group(1)
        event_key = event_key.replace('--', '-')
        
        # Update UI
        self.load_button.setEnabled(False)
        self.load_button.setText("Loading...")
        self.status_label.setText("Loading event data...")
        self.status_bar.setValue(0)
        
        # Save the event key
        self.settings.setValue("event/key", event_key)
        
        # Start the loader thread
        self.loader_thread = EventLoaderThread(event_key, self.get_proxy_string())
        self.loader_thread.progress_signal.connect(self.update_load_progress)
        self.loader_thread.result_signal.connect(self.process_event_data)
        self.loader_thread.error_signal.connect(self.handle_load_error)
        self.loader_thread.start()
        
        self.log(f"Loading event data for {event_key}...")
        
    def update_load_progress(self, stage, percent):
        """Update loading progress indicators"""
        self.status_label.setText(stage)
        self.status_bar.setValue(percent)
        
    def process_event_data(self, event_data):
        """Process loaded event data"""
        self.event_data = event_data
        
        # Reset UI
        self.load_button.setEnabled(True)
        self.load_button.setText("Load Event")
        self.status_label.setText("Event loaded successfully")
        self.status_bar.setValue(100)
        
        # Extract event details
        event_name = event_data.get("data", {}).get("title", "Unknown Event")
        self.log(f"Successfully loaded event: {event_name}")
        
        # Store channel keys
        self.channel_keys = event_data.get("data", {}).get("channel_keys", ["NO_CHANNEL"])
        
        # Extract team information
        self.teams = {}
        self.team_combo.clear()
        
        if "home_team" in event_data.get("data", {}) and event_data["data"]["home_team"]:
            home_team = event_data["data"]["home_team"]
            self.teams["home"] = {
                "name": home_team["name"],
                "id": home_team["_id"]
            }
            self.team_combo.addItem(f"Home: {home_team['name']}", home_team["_id"])
            
        if "away_team" in event_data.get("data", {}) and event_data["data"]["away_team"]:
            away_team = event_data["data"]["away_team"]
            self.teams["away"] = {
                "name": away_team["name"],
                "id": away_team["_id"]
            }
            self.team_combo.addItem(f"Away: {away_team['name']}", away_team["_id"])
            
        # Enable seat switching
        self.switch_button.setEnabled(True)
        
    def handle_load_error(self, error_msg):
        """Handle error during event loading"""
        self.load_button.setEnabled(True)
        self.load_button.setText("Load Event")
        self.status_label.setText("Error loading event")
        self.status_bar.setValue(0)
        
        self.log(f"Error: {error_msg}")
        QMessageBox.critical(self, "Error", f"Failed to load event:\n{error_msg}")
        
    def switch_seat(self):
        """Switch a seat between tokens"""
        # Validate inputs
        seat_number = self.seat_edit.text().strip()
        old_token = self.old_token_edit.text().strip()
        new_token = self.new_token_edit.text().strip()
        
        if not seat_number:
            QMessageBox.warning(self, "Warning", "Please enter a seat number.")
            return
            
        if not old_token:
            QMessageBox.warning(self, "Warning", "Please enter the old token.")
            return
            
        if not new_token:
            QMessageBox.warning(self, "Warning", "Please enter the new token.")
            return
            
        if not self.event_data or not self.event_data.get("data", {}).get("seats_io", {}).get("event_key"):
            QMessageBox.warning(self, "Warning", "Please load an event first.")
            return
            
        # Add to history as "Pending"
        start_time = time.time()
        self.history_table.add_operation(
            datetime.now(),
            seat_number,
            old_token,
            new_token,
            "Pending"
        )
        
        # Update UI
        self.switch_button.setEnabled(False)
        self.status_label.setText("Switching seat...")
        self.status_bar.setValue(10)
        
        # Get event key and team ID
        event_key = self.event_data["data"]["seats_io"]["event_key"]
        team_id = self.team_combo.currentData() if self.team_combo.currentIndex() >= 0 else None
        
        # Log the operation
        self.log(f"Switching seat {seat_number} from token {old_token} to {new_token}...")
        
        # Perform the switch
        try:
            # Use the imported switch_seat_immediate function
            success = switch_seat_immediate(
                seat_number=seat_number,
                event_key=event_key,
                old_token=old_token,
                new_token=new_token,
                channel_keys=self.channel_keys,
                team_id=team_id,
                proxy=self.get_proxy_string()
            )
            
            # Calculate duration
            duration = time.time() - start_time
            
            # Update history and log
            last_row = self.history_table.rowCount() - 1
            if success:
                status = "Success"
                self.log(f"Successfully switched seat {seat_number} in {duration:.3f}s")
                self.status_label.setText("Seat switched successfully")
                self.status_bar.setValue(100)
            else:
                status = "Failed"
                self.log(f"Failed to switch seat {seat_number} after {duration:.3f}s")
                self.status_label.setText("Seat switch failed")
                self.status_bar.setValue(0)
                
            # Update the pending entry in history
            self.history_table.setItem(last_row, 4, QTableWidgetItem(status))
            self.history_table.setItem(last_row, 5, QTableWidgetItem(f"{duration:.3f}s"))
            
            # Style the status cell
            status_item = self.history_table.item(last_row, 4)
            if status == "Success":
                status_item.setForeground(Qt.darkGreen)
            else:
                status_item.setForeground(Qt.red)
                
        except Exception as e:
            # Handle any exceptions
            duration = time.time() - start_time
            
            # Check for token mismatch error
            error_str = str(e)
            if "because the hold token IDs don" in error_str:
                status = "Ticket Taken"
                self.log(f"Failed to switch seat {seat_number}: Someone has already taken this ticket")
                self.status_label.setText("Seat switch failed - Ticket already taken")
            else:
                status = "Error"
                self.log(f"Error switching seat: {error_str}")
                self.status_label.setText("Error switching seat")
            
            # Update history
            last_row = self.history_table.rowCount() - 1
            self.history_table.setItem(last_row, 4, QTableWidgetItem(status))
            self.history_table.setItem(last_row, 5, QTableWidgetItem(f"{duration:.3f}s"))
            
            # Style the status cell
            status_item = self.history_table.item(last_row, 4)
            if status == "Ticket Taken":
                status_item.setForeground(Qt.blue)  # Use blue for ticket taken scenario
            else:
                status_item.setForeground(Qt.red)
            
            self.status_bar.setValue(0)
            
            # Show error message
            if status == "Ticket Taken":
                QMessageBox.warning(self, "Warning", f"This ticket has already been taken by someone else.\nSeat: {seat_number}")
            else:
                QMessageBox.critical(self, "Error", f"Error switching seat:\n{error_str}")
            
        finally:
            # Re-enable the switch button
            self.switch_button.setEnabled(True)            
    def export_history(self):
        """Export operation history to CSV file"""
        if self.history_table.rowCount() == 0:
            QMessageBox.information(self, "Info", "No operations to export.")
            return
            
        # Get filename from user
        filename, _ = QFileDialog.getSaveFileName(
            self,
            "Export Operation History",
            f"seat_operations_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
            "CSV Files (*.csv)"
        )
        
        if not filename:
            return
            
        # Export to CSV
        if self.history_table.export_to_csv(filename):
            self.log(f"History exported to {filename}")
            QMessageBox.information(self, "Success", f"History exported to {filename}")
        else:
            QMessageBox.critical(self, "Error", "Failed to export history.")
            
if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setStyle("Fusion")  # Use Fusion style for consistent look
    window = SeatSwitcherApp()
    window.show()
    sys.exit(app.exec_())
