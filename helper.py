import requests
import hashlib
import json
import time
from seleniumbase import BaseCase, Driver
from selenium_stealth import stealth
import random
import uuid
import os
import shutil
from selenium.webdriver import Chrome
from selenium.common.exceptions import WebDriverException
from collections import defaultdict
import httpx
from lxml import html
import base64
import hwid
import threading
from tenacity import retry, stop_after_attempt, wait_fixed
import logging
from chart_token_manager import generate_x_signature as central_generate_x_signature
from token_retrieval import get_hold_token as new_get_hold_token, get_cached_event_id



logger = logging.getLogger("webook_pro")


def get_random_useragent(file_path='useragents.json'):
    if not hasattr(get_random_useragent, "useragents"):
        try:
            url = "https://useragents.me/"
            response = requests.get(url, timeout=5)
            logger.debug('Got user agents successfully')
            tree = html.fromstring(response.content)
            xpath_expression = '//div[@id="most-common-desktop-useragents-json-csv"]/div[h3[text()="JSON"]]/textarea'
            json_text = tree.xpath(xpath_expression)[0].text
            get_random_useragent.useragents = json.loads(json_text)
            with open(file_path, 'w') as f:
                json.dump(get_random_useragent.useragents, f)
            logger.debug(f'User agents saved to {file_path}')
        
        except Exception as e:
            logger.error(f'Failed to fetch user agents online. Error: {e}')
            # Load from file if online request fails
            if os.path.exists(file_path):
                with open(file_path, 'r') as f:
                    get_random_useragent.useragents = json.load(f)
                logger.info(f'User agents loaded from {file_path}')
            else:
                raise RuntimeError(f"No user agents available online or offline at {file_path}")

    while True:
        yield random.choice(get_random_useragent.useragents)
user_agents = get_random_useragent()
next(user_agents)
def get_profile_info(bearer:str) -> dict:
    headers = {
        'accept': 'application/json',
        'accept-language': 'en-US,en;q=0.9,en-GB;q=0.8,ar;q=0.7,fr;q=0.6',
        'authorization': f'Bearer {bearer}',
        'cache-control': 'no-cache',
        'content-type': 'application/json',
        'dnt': '1',
        'origin': 'https://webook.com',
        'pragma': 'no-cache',
        'priority': 'u=1, i',
        'sec-ch-ua': '"Microsoft Edge";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-site',
        'token': 'e9aac1f2f0b6c07d6be070ed14829de684264278359148d6a582ca65a50934d2',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0',
    }

    params = {
        'lang': 'ar',
    }

    response = requests.get('https://api.webook.com/api/v2/user/profile', params=params, headers=headers)
    return response.json()
    # info = get_profile_info("****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************")
    # print('Hello', info['data']['name'])
    # print('Balance:', info['data']['wallet']['balance'])

def initialize_driver(browser_args={}):        
        random_resolution_list = [
            '800x600', '1024x768', '1280x720', '1366x768', '1600x900', '1920x1080',
            '1536x864', '1440x900', '1280x800', '2560x1440', '2560x1600', '1680x1050'
        ]
        random_resolution = random.choice(random_resolution_list)
        d_width, d_height = random_resolution.split('x')
        # browser_args['incognito'] = random.choice([True, False])
        browser_args['incognito'] = False
        browser_args['multi_proxy'] = True
        chromium_args = [
            "--disable-plugins",
            "--disable-popup-blocking",
            "--disable-infobars",
            "--disable-notifications",
            "--disable-geolocation",
            "--disable-remote-fonts",
            "--disable-background-networking",
            "--disable-background-timer-throttling",
            "--disable-backgrounding-occluded-windows",
            "--disable-renderer-backgrounding",
            "--disable-web-security",
            "--disable-client-side-phishing-detection",
            "--disable-component-update",
            "--disable-default-apps",
            "--disable-domain-reliability",
            "--disable-features=site-per-process",
            "--disable-hang-monitor",
            "--disable-ipc-flooding-protection",
            "--disable-prompt-on-repost",
            "--disable-sync",
            "--disable-translate",
            "--disable-web-resources",
            "--disable-webgl",
            "--disable-webrtc",
            "--disable-xss-auditor",
            "--no-sandbox",
            "--ignore-certificate-errors",
            "--disable-dev-shm-usage",
            "--disable-gpu",
            "--disable-software-rasterizer",
            "--force-color-profile=srgb",
            "--window-size={},{}".format(d_width, d_height),
        ]
        
        # Randomly select a subset of arguments
        selected_args = random.sample(chromium_args, k=random.randint(5, len(chromium_args)))
        chromium_arg = ",".join(selected_args)
        browser_args['chromium_arg'] = chromium_arg
        
        driver = Driver(**browser_args)
        
        # Expanded language and vendor options
        languages_options = [
            ["en-US", "en"], ["fr-FR", "fr"], ["es-ES", "es"], ["de-DE", "de"], 
            ["it-IT", "it"], ["ja-JP", "ja"], ["pt-BR", "pt"], ["ru-RU", "ru"],
            ["zh-CN", "zh"], ["ar-SA", "ar"], ["ko-KR", "ko"], ["nl-NL", "nl"],
            ["sv-SE", "sv"], ["no-NO", "no"], ["fi-FI", "fi"], ["da-DK", "da"],
            ["tr-TR", "tr"], ["pl-PL", "pl"], ["hu-HU", "hu"], ["cs-CZ", "cs"],
            ["el-GR", "el"], ["he-IL", "he"], ["ro-RO", "ro"], ["sk-SK", "sk"],
            ["th-TH", "th"], ["id-ID", "id"], ["vi-VN", "vi"], ["uk-UA", "uk"]
        ]

        vendor_options = [
            "Google Inc.", "Apple Inc.", "Microsoft Corporation", "Mozilla Foundation",
            "Samsung Electronics", "Opera Software", "Facebook Inc.", "Adobe Inc.",
            "Oracle Corporation", "IBM Corporation", "Nokia Corporation", "Xiaomi Inc.",
            "Huawei Technologies Co. Ltd.", "ASUSTeK Computer Inc.", "Dell Inc.",
            "Hewlett-Packard Company", "Lenovo Group Ltd.", "Sony Corporation"
        ]

        platform_options = [
            "Win32", "Win64", "MacIntel", "Linux x86_64", "Linux i686", "FreeBSD x86_64",
            "MacPPC", "SunOS i86pc", "Solaris x86", "Android", "iPhone", "iPad",
            "CrOS x86_64", "CrOS armv7l"
        ]

        webgl_vendor_options = [
            "Intel Inc.", "NVIDIA Corporation", "AMD Inc.", "ATI Technologies Inc.",
            "Qualcomm", "ARM", "Broadcom Inc.", "Imagination Technologies", "Vivante Corporation",
            "ARM Mali", "PowerVR", "Microsoft Direct3D", "Mesa Project", "Intel (R) HD Graphics",
            "NVIDIA Tegra"
        ]

        renderer_options = [
            "Intel Iris OpenGL Engine", "AMD Radeon Pro", "NVIDIA GeForce GTX",
            "ANGLE (Intel, Intel(R) HD Graphics)", "ANGLE (NVIDIA, NVIDIA Quadro)",
            "ANGLE (AMD, AMD Radeon)", "Apple A13 GPU", "Apple M1 GPU", "Adreno (TM) 630",
            "AMD Radeon RX Vega", "AMD Radeon RX 6800", "NVIDIA GeForce RTX 3080",
            "NVIDIA GeForce MX150", "Intel HD Graphics 4000",
            "ANGLE (Intel, Intel(R) UHD Graphics)", "ANGLE (NVIDIA, NVIDIA GeForce RTX)",
            "ANGLE (AMD, AMD FirePro)", "ANGLE (Qualcomm, Adreno (TM) 530)",
            "ANGLE (Intel, Intel(R) Iris Plus Graphics)", "ANGLE (NVIDIA, NVIDIA Tesla)",
            "ANGLE (AMD, AMD Radeon RX Vega M)", "ARM Mali-G71", "Apple M1 Pro GPU",
            "PowerVR Rogue", "Vivante GC7000", "Imagination Technologies PowerVR Series8",
            "Mesa DRI Intel(R) UHD Graphics 620"
        ]
        fix_hairline_options = [True, False]
        
        selected_languages = random.choice(languages_options)
        selected_vendor = random.choice(vendor_options)
        selected_platform = random.choice(platform_options)
        selected_webgl_vendor = random.choice(webgl_vendor_options)
        selected_renderer = random.choice(renderer_options)
        selected_fix_hairline = random.choice(fix_hairline_options)
        
        stealth(
            driver,
            languages=selected_languages,
            vendor=selected_vendor,
            platform=selected_platform,
            webgl_vendor=selected_webgl_vendor,
            renderer=selected_renderer,
            user_agent=next(user_agents)['ua'],
            run_on_insecure_origins=True,
            fix_hairline=selected_fix_hairline
        )
        
        debug_info = {
        "resolution": random_resolution,
        "incognito": browser_args['incognito'],
        "chromium_args": selected_args,
        "languages": selected_languages,
        "vendor": selected_vendor,
        "platform": selected_platform,
        "webgl_vendor": selected_webgl_vendor,
        "renderer": selected_renderer,
        "fix_hairline": selected_fix_hairline
    }
        driver:BaseCase = driver
        return driver, debug_info


def build_channel_keys(channel_keys, team_id):
    """
    Optimized channel keys builder with caching and fail-fast approach.
    
    Args:
        channel_keys: Input channel keys (dict or list)
        team_id: Team ID for channel selection
        
    Returns:
        List of applicable channel keys
    """
    # Fast path - direct return for simple case
    if not channel_keys or channel_keys == ['NO_CHANNEL']:
        return ['NO_CHANNEL']
        
    # Use function-level caching to avoid repeated work
    # The cache key is a tuple of (channel_keys_id, team_id)
    if not hasattr(build_channel_keys, '_cache'):
        build_channel_keys._cache = {}
        
    # Convert channel_keys to hashable format for cache key
    if isinstance(channel_keys, dict):
        cache_key = (str(sorted(channel_keys.keys())), team_id)
    else:
        cache_key = (str(channel_keys), team_id)
        
    # Check cache first
    if cache_key in build_channel_keys._cache:
        return build_channel_keys._cache[cache_key]
    
    # Build the channel keys list
    keys = []
    
    # Dictionary-based channel keys
    if isinstance(channel_keys, dict):
        # Check for common channel and add it first
        if 'common' in channel_keys and channel_keys['common']:
            keys.append('NO_CHANNEL')
            keys.append(channel_keys['common'][0])
            
            # Try to add team-specific channel
            if team_id and team_id in channel_keys and channel_keys[team_id]:
                keys.append(channel_keys[team_id][0])
            else:
                # Fall back to first non-common channel
                for k in channel_keys:
                    if k != 'common' and channel_keys[k]:
                        keys.append(channel_keys[k][0])
                        break
    
    # If keys is still empty, use defaults
    if not keys:
        keys.append('NO_CHANNEL')
        if team_id and isinstance(channel_keys, dict) and team_id in channel_keys and channel_keys[team_id]:
            keys.append(channel_keys[team_id][0])
    
    # Cache the result
    build_channel_keys._cache[cache_key] = keys
    
    return keys


def switch_seat_immediate(
    seat_number: str,
    event_key: str,
    old_token: str,
    new_token: str,
    channel_keys=None,
    team_id=None,
    proxy: str = None,
    max_attempts=2
) -> bool:
    """
    Ultra-optimized seat switching with minimal gap between release and hold operations.
    Uses persistent connection pool, request pipelining, and HTTP/2 prioritization.
    
    Args:
        seat_number: The ID of the seat to switch
        event_key: Event identifier
        old_token: Current token holding the seat
        new_token: New token to hold the seat with
        channel_keys: Channel keys for the hold operation
        team_id: Team ID for channel key selection
        proxy: Optional proxy string
        max_attempts: Maximum number of retry attempts
    
    Returns:
        True if successful, False otherwise
    """    
    logger = logging.getLogger("webook_pro")
    total_start_time = time.time()
    thread_name = threading.current_thread().name
    
    # Rename current thread for better debugging
    threading.current_thread().name = f"SeatSwitch-{seat_number}"
    
    # Pre-setup phase - do all preparation outside the critical path
    if channel_keys is None:
        channel_keys = ['NO_CHANNEL']

    # Shared browser ID for both requests
    shared_browser_id = ''.join(f"{random.randint(0, 0xFFFF):04x}" for _ in range(4))
    
    # Prepare request bodies
    release_json = {
        'events': [event_key],
        'holdToken': old_token,
        'objects': [{'objectId': seat_number}],
        'validateEventsLinkedToSameChart': True,
    }
    release_body = json.dumps(release_json, separators=(',', ':'))
    
    hold_keys = build_channel_keys(channel_keys, team_id)
    hold_json = {
        'events': [event_key],
        'holdToken': new_token,
        'objects': [{'objectId': seat_number}],
        'channelKeys': hold_keys,
        'validateEventsLinkedToSameChart': True,
    }
    hold_body = json.dumps(hold_json, separators=(',', ':'))

    # Generate signatures
    release_sig = generate_x_signature(release_body)
    hold_sig = generate_x_signature(hold_body)

    # Prepare headers - add HTTP/2 priority hints
    release_headers = {
        'accept': '*/*',
        'accept-language': 'en-US,en;q=0.9',
        'content-type': 'application/json',
        'origin': 'https://cdn-eu.seatsio.net',
        'priority': 'u=3',  # High priority
        'x-browser-id': shared_browser_id,
        'x-client-tool': 'Renderer',
        'x-signature': release_sig,
    }
    
    hold_headers = {
        'accept': '*/*',
        'accept-language': 'en-US,en;q=0.9',
        'content-type': 'application/json',
        'origin': 'https://cdn-eu.seatsio.net',
        'priority': 'u=1',  # Highest priority
        'x-browser-id': shared_browser_id,
        'x-client-tool': 'Renderer',
        'x-signature': hold_sig,
    }

    # Endpoints
    base_url = 'https://cdn-eu.seatsio.net/system/public/3d443a0c-83b8-4a11-8c57-3db9d116ef76/events/groups/actions/'
    release_url = f"{base_url}release-held-objects"
    hold_url = f"{base_url}hold-objects"

    # Configure proxy
    proxies = None
    if proxy:
        parts = proxy.split(":")
        if len(parts) == 4:
            proxy_host, proxy_port, proxy_user, proxy_pass = parts
            proxies = {
                "http://": f"http://{proxy_user}:{proxy_pass}@{proxy_host}:{proxy_port}",
                "https://": f"http://{proxy_user}:{proxy_pass}@{proxy_host}:{proxy_port}",
            }
        elif len(parts) == 2:
            proxy_host, proxy_port = parts
            proxies = {
                "http://": f"http://{proxy_host}:{proxy_port}",
                "https://": f"http://{proxy_host}:{proxy_port}",
            }
        elif len(parts) == 3:
            proxies = {
                "http": f"http://{proxy}",
                "https": f"http://{proxy}",
            }

    # Fatal error patterns
    FATAL_ERROR_PATTERNS = [
        "not in status reservedByToken",
        "no longer available",
        "has already been reserved",
        "token is not valid",
        "token has expired"
    ]

    # Timing data collection
    best_release_time = float('inf')
    best_gap_time = float('inf')
    best_hold_time = float('inf')
    total_attempts = 0
    success = False

    # Global connection pool management (thread-safe)
    client_pool_lock = threading.RLock()
    
    # Initialize static connection pool if it doesn't exist
    if not hasattr(switch_seat_immediate, '_client_pool'):
        switch_seat_immediate._client_pool = {}
    
    # Client pool key based on proxy settings
    client_pool_key = str(proxies)
    
    # Get or create client
    with client_pool_lock:
        if client_pool_key in switch_seat_immediate._client_pool:
            client = switch_seat_immediate._client_pool[client_pool_key]
        else:
            # Configure advanced client for ultra-fast switching
            transport = httpx.HTTPTransport(
                http2=True,
                retries=1,
                limits=httpx.Limits(
                    max_connections=100,
                    max_keepalive_connections=20,
                    keepalive_expiry=30  # Keep connections alive longer
                )
            )
            
            # Create client with very short timeout
            client = httpx.Client(
                transport=transport,
                timeout=httpx.Timeout(
                    connect=0.5,   # Connect timeout in seconds
                    read=0.5,      # Read timeout in seconds
                    write=0.5,     # Write timeout in seconds
                    pool=1.0       # Pool timeout in seconds
                ),
                http2=True
            )
            
            # Add proxy if configured
            if proxies:
                client.proxies = proxies
                
            # Store in pool
            switch_seat_immediate._client_pool[client_pool_key] = client

    # Try to pipeline the requests
    try:
        for attempt in range(1, max_attempts + 1):
            total_attempts = attempt
            
            try:
                # 1) RELEASE with very short timeout
                release_start = time.time()
                release_resp = client.post(
                    release_url, 
                    headers=release_headers, 
                    content=release_body,
                    timeout=0.75  # Ultra-short timeout
                )
                release_end = time.time()
                release_time = release_end - release_start
                best_release_time = min(best_release_time, release_time)

                if release_resp.status_code != 204:
                    error_message = release_resp.text
                    
                    # Check for fatal errors
                    fatal_error = any(pattern in error_message for pattern in FATAL_ERROR_PATTERNS)
                    if fatal_error:
                        logger.warning(f"[SWITCH] Fatal error releasing seat {seat_number}: {error_message}")
                        break
                    
                    # Only log detailed error on first attempt to reduce log spam
                    if attempt == 1:
                        logger.warning(f"[SWITCH] Release failed: {release_resp.status_code} - {error_message}")
                    
                    # Non-fatal error, will retry
                    continue
                
                # Start preparing hold request IMMEDIATELY after successful release
                # Gap timing starts here
                gap_start = time.time()
                
                # Create pre-prepared hold request for speed
                hold_req = client.build_request("POST", hold_url, headers=hold_headers, content=hold_body)
                
                # 2) HOLD - Execute immediately with no delay
                hold_start = time.time()
                hold_resp = client.send(hold_req)
                hold_end = time.time()
                
                # Calculate timing metrics
                gap_time = hold_start - gap_start
                hold_time = hold_end - hold_start
                best_gap_time = min(best_gap_time, gap_time)
                best_hold_time = min(best_hold_time, hold_time)

                if hold_resp.status_code != 204:
                    error_message = hold_resp.text
                    
                    # If seat was taken by someone else, stop trying
                    if "has already been reserved" in error_message:
                        logger.warning(f"[SWITCH] Seat {seat_number} was taken by someone else during switch")
                        break
                    
                    # Only log detailed error on first attempt
                    if attempt == 1:
                        logger.warning(f"[SWITCH] Hold failed: {hold_resp.status_code} - {error_message[:100]}")
                    
                    # Non-fatal error, will retry
                    continue
                
                # Success!
                success = True
                break

            except (httpx.RequestError, httpx.TimeoutException) as e:
                # Network errors - retry immediately with shorter timeout for next attempt
                if attempt == 1:
                    logger.warning(f"[SWITCH] Network error: {str(e)[:100]}")
                continue
            except Exception as e:
                # Unexpected errors - retry
                if attempt == 1:
                    logger.warning(f"[SWITCH] Unexpected error: {str(e)[:100]}")
                continue

    except Exception as e:
        logger.error(f"[SWITCH] Exception for seat {seat_number}: {str(e)}")
    
    finally:
        # Restore original thread name
        threading.current_thread().name = thread_name
        
        # Calculate total time and log detailed timing information
        total_time = time.time() - total_start_time
        
        if success:
            logger.info(
                f"✅ SWITCHED seat {seat_number} in {total_time:.3f}s "
                f"[{total_attempts}/{max_attempts}] "
                f"Release: {best_release_time:.3f}s | "
                f"Gap: {best_gap_time*1000:.1f}ms | "  # Report gap in milliseconds for clarity
                f"Hold: {best_hold_time:.3f}s"
            )
        else:
            logger.error(
                f"❌ FAILED to switch seat {seat_number} after {total_time:.3f}s "
                f"[{total_attempts}/{max_attempts}]"
            )
    
    return success

def get_webook_event_info(event_key: str):
    """
    Get event details from Webook API using http.client.HTTPSConnection
    """
    import http.client
    import json
    import urllib.parse
    
    # Setup headers
    headers = {
        'accept': 'application/json',
        'accept-language': 'en-US,en;q=0.9,en-GB;q=0.8,ar;q=0.7,fr;q=0.6',
        'cache-control': 'no-cache',
        'content-type': 'application/json',
        'dnt': '1',
        'origin': 'https://webook.com',
        'sec-ch-ua': '"Microsoft Edge";v="135", "Not-A.Brand";v="8", "Chromium";v="135"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-site',
        'pragma': 'no-cache',
        'priority': 'u=1, i',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0',
        'token': 'e9aac1f2f0b6c07d6be070ed14829de684264278359148d6a582ca65a50934d2'
    }

    # Setup params
    params = {
        'lang': 'en',
        'visible_in': 'rs',
    }
    
    # Create query string
    query_string = urllib.parse.urlencode(params)
    
    # Create connection
    conn = http.client.HTTPSConnection('api.webook.com')
    
    # Send request
    conn.request(
        'GET', 
        f'/api/v2/event-detail/{event_key}?{query_string}', 
        headers=headers
    )
    
    # Get response
    response = conn.getresponse()
    
    # Create a response-like object to match the expected interface
    class ResponseWrapper:
        def __init__(self, http_response):
            self.status = http_response.status
            self.reason = http_response.reason
            self._data = http_response.read()
            self.ok = 200 <= self.status < 300
            self.is_success = self.ok
        
        def json(self):
            return json.loads(self._data.decode('utf-8'))
    
    wrapped_response = ResponseWrapper(response)
    
    # If response is successful, cache the event ID
    if wrapped_response.ok:
        try:
            event_data = wrapped_response.json()
            # Cache the event ID with the event key
            from token_retrieval import cache_event_id
            cache_event_id(event_data)
        except Exception as e:
            import logging
            logger = logging.getLogger('webook_pro')
            logger.error(f"Error caching event ID: {str(e)}")
    
    conn.close()
    return wrapped_response



def get_match_info(chart_key:str, drawVersion:int ) -> dict:
    headers = {
    'accept': '*/*',
    'accept-language': 'en-US,en;q=0.9,en-GB;q=0.8,ar;q=0.7,fr;q=0.6',
    'cache-control': 'no-cache',
    'dnt': '1',
    'pragma': 'no-cache',
    'priority': 'u=1, i',
    'referer': 'https://cdn-eu.seatsio.net/static/version/seatsio-ui-prod-00221-fff/chart-renderer/chartRendererIframe.html?environment=PROD&commit_hash=0016c9aa2b5f7186fb3e72a702e536aa2250d77a',
    'sec-ch-ua': '"Microsoft Edge";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'same-origin',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0',
    'x-browser-id': ''.join(f"{random.randint(0, 0xFFFF):04x}" for _ in range(4)),
    'x-client-tool': 'Renderer',
    'x-kl-saas-ajax-request': 'Ajax_Request',
    'x-signature': generate_x_signature(""),
    }

    response = requests.get(
        f'https://cdn-eu.seatsio.net/system/public/3d443a0c-83b8-4a11-8c57-3db9d116ef76/charts/{chart_key}/published/{drawVersion}',
        headers=headers,
    )
    match_data = VM.deobfuscate(response.content, chart_key)
    return json.loads(match_data)

def get_object_statuses(event_key:str, chart_key:str) -> dict:
        headers = {
        'accept': '*/*',
        'accept-language': 'en-US,en;q=0.9,en-GB;q=0.8,ar;q=0.7,fr;q=0.6',
        'cache-control': 'no-cache',
        'dnt': '1',
        'pragma': 'no-cache',
        'priority': 'u=1, i',
        'referer': 'https://cdn-eu.seatsio.net/static/version/seatsio-ui-prod-00221-fff/chart-renderer/chartRendererIframe.html?environment=PROD&commit_hash=0016c9aa2b5f7186fb3e72a702e536aa2250d77a',
        'sec-ch-ua': '"Microsoft Edge";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0',
        'x-browser-id': ''.join(f"{random.randint(0, 0xFFFF):04x}" for _ in range(4)),
        'x-client-tool': 'Renderer',
        'x-kl-saas-ajax-request': 'Ajax_Request',
        'x-signature': generate_x_signature(""),
    }

        params = {
            'event_key': event_key,
        }

        response = requests.get(
            'https://cdn-eu.seatsio.net/system/public/3d443a0c-83b8-4a11-8c57-3db9d116ef76/events/object-statuses',
            params=params,
            headers=headers,
            proxies = {
                "http://": f"http://ucegtvkm-rotate:<EMAIL>:80",
                "https://": f"http://ucegtvkm-rotate:<EMAIL>:80"
        }
        )
        encrypted_file = response.content
        objects = VM.deobfuscate(encrypted_file, chart_key)
        objects = json.loads(objects)
        # sort by objectLabelOrUuid in a list of dict by the objectLabelOrUuid key
        objects = sorted(objects, key=lambda x: x['objectLabelOrUuid'])
        return objects
    

def get_event_seatsio_info(seatsio_data:str) -> dict:
    data = seatsio_data
    chart_key = data['chart_key']
    event_key = data['event_key']
    render_data = get_event_render_data(event_key)
    drawversion = render_data['drawingVersion']
    season_info = render_data.get('seasonStructure', None)
    match_data = get_match_info(chart_key, drawversion)
    match_data['season_info'] = season_info
    return match_data
    

    
def get_render_tickets(data:dict) -> list:
    tickets = []
    for i in data['channels']:
        for ticket in i['objects']:
            tickets.append(ticket)
    return tickets

def get_event_render_data(event_key:str) -> dict:

    headers = {
        'accept': '*/*',
        'accept-language': 'en-US,en;q=0.9,en-GB;q=0.8,ar;q=0.7,fr;q=0.6',
        'cache-control': 'no-cache',
        'dnt': '1',
        'pragma': 'no-cache',
        'priority': 'u=1, i',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0',
        'x-browser-id': ''.join(f"{random.randint(0, 0xFFFF):04x}" for _ in range(4)),
        'x-client-tool': 'Renderer',
        'x-kl-saas-ajax-request': 'Ajax_Request',
        'x-request-origin': 'webook.com',
        'x-signature': generate_x_signature(""),
    }

    params = {
        'event_key': event_key,
    }

    response = requests.get(
        'https://cdn-eu.seatsio.net/system/public/3d443a0c-83b8-4a11-8c57-3db9d116ef76/rendering-info',
        params=params,
        headers=headers,
        proxies = {
                "http://": f"http://ucegtvkm-rotate:<EMAIL>:80",
                "https://": f"http://ucegtvkm-rotate:<EMAIL>:80"
        })

    if response.status_code == 200:
        data = response.json()
        return data
    return {'Error':response.status_code}
# print(get_event_render_info('abdul-majeed-abdullah-night-24'))

def get_ticket_types(tickets):
    ticket_counts = {}
    for ticket in tickets:
        if '-' in ticket:
            ticket = ticket.split('-')[0]
        if ticket in ticket_counts:
            ticket_counts[ticket] += 1
        else:
            ticket_counts[ticket] = 1
    return ticket_counts

def get_hold_token(proxy=None):
    """
    Updated function to get hold token using the new API method.
    Maintains the same signature for backwards compatibility.
    
    Args:
        proxy: Optional proxy string
        
    Returns:
        Hold token string or None if failed
    """
    # Get cached event ID
    event_id = get_cached_event_id()
    
    if not event_id:
        import logging
        logger = logging.getLogger('webook_pro')
        logger.error("No event ID available for token request, please load an event first")
        return None
    
    # Call the new implementation with the event ID
    return new_get_hold_token(event_id=event_id, proxy=proxy)


def activate_hold_token(token, proxy=None):
    """
    Activate a hold token to get its expiration time.
    This function remains largely unchanged as the token activation
    still uses the seatsio API.
    """
    import requests
    import random
    import logging
    
    logger = logging.getLogger('webook_pro')
    
    headers = {
        'accept': '*/*',
        'accept-language': 'en-US,en;q=0.9,en-GB;q=0.8,ar;q=0.7,fr;q=0.6',
        'cache-control': 'no-cache',
        'dnt': '1',
        'pragma': 'no-cache',
        'priority': 'u=1, i',
        'referer': 'https://cdn-eu.seatsio.net/static/version/seatsio-ui-prod-00219-x7p/chart-renderer/chartRendererIframe.html?environment=PROD&commit_hash=04cfc850e5aa11b7b98d6e39d603ae06f1eaa9ff',
        'sec-ch-ua': '"Microsoft Edge";v="135", "Not-A.Brand";v="8", "Chromium";v="135"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0',
        'x-browser-id': ''.join(f"{random.randint(0, 0xFFFF):04x}" for _ in range(4)),
        'x-client-tool': 'Renderer',
        'x-kl-saas-ajax-request': 'Ajax_Request',
        'x-signature': '',  # Updated signature method is handled in chart_token_manager.py
    }
    
    proxies = None
    if proxy:
        parts = proxy.split(":")
        if len(parts) == 4:
            proxy_host, proxy_port, proxy_user, proxy_pass = parts
            proxies = {
                "http": f"http://{proxy_user}:{proxy_pass}@{proxy_host}:{proxy_port}",
                "https": f"http://{proxy_user}:{proxy_pass}@{proxy_host}:{proxy_port}",
            }
        elif len(parts) == 2:
            proxy_host, proxy_port = parts
            proxies = {
                "http": f"http://{proxy_host}:{proxy_port}",
                "https": f"http://{proxy_host}:{proxy_port}",
            }
        elif len(parts) == 3:
            proxies = {
                "http": f"http://{proxy}",
                "https": f"http://{proxy}",
            }
        else:
            logger.error(f"Invalid proxy format: {proxy}")
            return False

    try:
        # Import generate_x_signature dynamically to avoid circular imports
        from chart_token_manager import generate_x_signature
        # Update headers with signature
        headers['x-signature'] = generate_x_signature("")
        
        response = requests.get(
            f'https://cdn-eu.seatsio.net/system/public/3d443a0c-83b8-4a11-8c57-3db9d116ef76/hold-tokens/{token}',
            headers=headers, 
            proxies=proxies,
            timeout=5,
        )
        if response.status_code == 200:
            return response.json()['expiresInSeconds']
        else:
            logger.warning(f"Failed to activate token {token}: {response.status_code} - {response.text}")
            return 590  # Return a default expiration time
    except Exception as e:
        logger.error(f"Error activating hold token: {str(e)}")
        return 590  # Return a default expiration time


def test_proxy(proxy_url, target_url="https://www.google.com", timeout=5):
    """Tests a single proxy URL against a target URL.

    Args:
        proxy_url: The proxy URL string (e.g., "*********************:port").
        target_url: The URL to test the proxy against (default: Google).
        timeout: The timeout in seconds for the request (default: 5).

    Returns:
        A dictionary containing the proxy URL, status (True/False),
        status code (if successful), and any error message (if failed).
    """

    proxies = {
        "http": proxy_url,
        "https": proxy_url,  # Use the same proxy for both unless different
    }

    result = {"proxy": proxy_url, "status": False, "status_code": None, "error": None}

    try:
        response = requests.get(target_url, proxies=proxies, timeout=timeout)
        response.raise_for_status()  # Raise HTTPError for bad responses (4xx or 5xx)
        result["status"] = True
        result["status_code"] = response.status_code
    except requests.exceptions.RequestException as e:
        result["error"] = str(e)  # Store the error message
    except Exception as e: # catch other exceptions
        result["error"] = str(e)

    return result

def generate_x_signature(body: str) -> str:
    """
    Forward to the centralized generate_x_signature function.
    This preserves backward compatibility with existing code.
    """
    return central_generate_x_signature(body)
class VM:
    @staticmethod
    def deobfuscate(e, t):
        i = VM.key_to_number_between_0_and_63(t)
        n = bytearray(e)
        for j in range(len(n)):
            n[j] = n[j] - i
        return n.decode('utf-8')

    @staticmethod
    def key_to_number_between_0_and_63(e):
        return 63 & VM.hash_code(e)

    @staticmethod
    def hash_code(e):
        t = 0
        for i in range(len(e)):
            t = (29 * t % 10007 + ord(e[i])) % 10007
        return t

def deobfuscate_file(input_file, key='9b672bc2-e78b-469a-af9e-88275ebb100b'):
    # Read the file as binary data
    with open(input_file, 'rb') as f:
        file_data = f.read()
    output_file = input_file + '.decoded.json'
    # Deobfuscate the data
    deobfuscated_data = VM.deobfuscate(file_data, key)
    
    # Write the deobfuscated data to the output file
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(deobfuscated_data)
    
    logger.info(f"Deobfuscated data has been written to {output_file}")


def hold_seat(seat_number, event, hold_token, channel_keys=['NO_CHANNEL',], team_id=None, proxy=None):
    if not hold_token:
        hold_token = get_hold_token(proxy)
    keys = []
    if channel_keys!= ['NO_CHANNEL',] and channel_keys['common']:
        keys.append('NO_CHANNEL')
        keys.append(channel_keys['common'][0])
        try:
            keys.append(channel_keys[team_id][0])
        except:
            for key in channel_keys:
                if key != 'common':
                    keys.append(channel_keys[key][0])
                    break
    if keys == []:
        keys.append('NO_CHANNEL')
        if team_id:
            if team_id in channel_keys:
                keys.append(channel_keys[team_id][0])
    json_data = {
        'events': [
            event,
        ],
        'holdToken': hold_token,
        'objects': [
            {
                'objectId': seat_number,
            },
        ],
        'channelKeys': keys or channel_keys,
        'validateEventsLinkedToSameChart': True,
    }
    proxies = None
    if proxy:
        parts = proxy.split(":")
        if len(parts) == 4:
            proxy_host = parts[0]
            proxy_port = int(parts[1])
            proxy_user = parts[2]
            proxy_pass = parts[3]
            proxies = {
                "http://": f"http://{proxy_user}:{proxy_pass}@{proxy_host}:{proxy_port}",
                "https://": f"http://{proxy_user}:{proxy_pass}@{proxy_host}:{proxy_port}",
            }
        elif len(parts) == 2:
            proxy_host = parts[0]
            proxy_port = int(parts[1])
            proxies = {
                "http://": f"http://{proxy_host}:{proxy_port}",
                "https://": f"http://{proxy_host}:{proxy_port}",
            }
        elif len(parts) == 3:
            proxies = {
                "http": f"http://{proxy}",
                "https": f"http://{proxy}",
            }

        else:
            logger.error(f"Invalid proxy format: {proxy}")
            return False

    body_str = json.dumps(json_data, separators=(',', ':'))
    x_sig = generate_x_signature(body_str)
    headers = {
        'accept': '*/*',
        'accept-language': 'en-US,en;q=0.9',
        'content-type': 'application/json',
        'origin': 'https://cdn-eu.seatsio.net',
        'priority': 'u=1, i',
        'referer': 'https://cdn-eu.seatsio.net/static/version/seatsio-ui-prod-00221-fff/chart-renderer/chartRendererIframe.html?environment=PROD&commit_hash=0016c9aa2b5f7186fb3e72a702e536aa2250d77a',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0',
        'x-browser-id': ''.join(f"{random.randint(0, 0xFFFF):04x}" for _ in range(4)),
        'x-client-tool': 'Renderer',
        'x-kl-saas-ajax-request': 'Ajax_Request',
        'x-signature': x_sig,
    }
    response = requests.post(
        'https://cdn-eu.seatsio.net/system/public/3d443a0c-83b8-4a11-8c57-3db9d116ef76/events/groups/actions/hold-objects',
        headers=headers,
        data=body_str,
        proxies=proxies
    )
    if response.status_code == 204:
        logger.info(f'reserved seat {seat_number} with hold token {hold_token}')
        return True
    else:
        logger.error(f'Error while holding seats, received status code {response.status_code} with the following text {response.text}')
        # print(response.status_code)
        # print(response.text)
        return False

def release_seat(seat_number, event, hold_token):
    json_data = {
        'events': [
            event,
        ],
        'holdToken': hold_token,
        'objects': [
            {
                'objectId': seat_number,
            },
        ],
        'validateEventsLinkedToSameChart': True,
    }
    data = json.dumps(json_data, separators=(',', ':'))
    x_sig = generate_x_signature(data)
    headers = {
        'accept': '*/*',
        'accept-language': 'en-US,en;q=0.9,en-GB;q=0.8,ar;q=0.7,fr;q=0.6',
        'cache-control': 'no-cache',
        'content-type': 'application/json',
        'dnt': '1',
        'origin': 'https://cdn-eu.seatsio.net',
        'pragma': 'no-cache',
        'priority': 'u=1, i',
        'referer': 'https://cdn-eu.seatsio.net/static/version/seatsio-ui-prod-00222-cvz/chart-renderer/chartRendererIframe.html?environment=PROD&commit_hash=4fa56eac9f91c69bd61582ac81d4dd6bd6f866f7',
        'sec-ch-ua': '"Microsoft Edge";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0',
        'x-browser-id': ''.join(f"{random.randint(0, 0xFFFF):04x}" for _ in range(4)),
        'x-client-tool': 'Renderer',
        'x-signature': x_sig,
    }


    response = requests.post(
        'https://cdn-eu.seatsio.net/system/public/3d443a0c-83b8-4a11-8c57-3db9d116ef76/events/groups/actions/release-held-objects',
        headers=headers,
        data=data,
    )
    if response.status_code == 204:
        logger.info(f'released seat {seat_number} with hold token {hold_token}')
        return True
    else:
        # print(response.status_code)
        # print(response.text)
        logger.error(f'Error while releasing seats, received status code {response.status_code} with the following text {response.text}')
        return False

def group_tickets_by_type_and_status(data, free_only=False):
    """
    Groups tickets by type and status using proper dictionary structure.
    Improved version that ensures all tickets are properly categorized.
    """
    ticket_groups = defaultdict(lambda: defaultdict(dict))  # type -> status -> {seat_id: seat_data}
    ticket_count = 0  # For debugging
    logging.info(f"Grouping {len(data)} tickets by type and status")

    for entry in data:
        ticket_count += 1
        # Extract type from objectLabelOrUuid - handle different formats
        if not entry.get('objectLabelOrUuid'):
            logging.warning(f"Found entry without objectLabelOrUuid: {entry}")
            continue

        seat_id = entry['objectLabelOrUuid']
        
        # Get status with fallback to 'free' if not specified
        status = entry.get('status', 'free').lower()
        
        # Skip if filtering for free_only and this isn't free
        if free_only and status != 'free':
            continue
            
        # Handle different label formats
        if '-' in seat_id:
            # Most common format: TYPE-SECTION-ROW-SEAT
            label_parts = seat_id.split('-')
            ticket_type = label_parts[0].strip()
        else:
            # No dash - use the whole ID as type
            ticket_type = seat_id
            
        # Special case handling for complex labels
        if not ticket_type:
            # Fallback if splitting resulted in empty string
            ticket_type = "UNKNOWN"
            logging.warning(f"Found empty ticket type for seat {seat_id}")
        
        # Add to the grouped data structure
        ticket_groups[ticket_type][status][seat_id] = entry

    # Log summary for debugging
    type_summary = {ttype: sum(len(statuses) for statuses in ticket_groups[ttype].values()) 
                  for ttype in ticket_groups}
    logging.info(f"Grouped into {len(ticket_groups)} ticket types: {type_summary}")
    
    return ticket_groups


def read_event(file):
    data = json.load(open(file, 'r'))
    grouped_data = group_tickets_by_type_and_status(data)
    print('hello')
# deobfuscate_file('52')
# # read_event('output.bin.decoded.json')
# tickets = get_event_render_tickets('Abdulmajeed-24')
# hold_seat('G1-35-5', 'italian-supercup-final-2025')
# # for ticket in tickets:
# #     if not 'Bronze' in ticket:
# #         continue
# #     reserve = hold_seat(ticket, event)
# #     if reserve:
# #         break
# # ticket_types = get_ticket_types(tickets)
# # print(ticket_types)
# print('done')

    
    
if __name__ == "__main__":
    test_proxy(proxy_url='http://taplmftg-rotate:<EMAIL>:80', target_url='https://elitesoftworks.com')