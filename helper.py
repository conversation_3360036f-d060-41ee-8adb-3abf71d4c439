import httpx
import hashlib
import json
import time
from seleniumbase import BaseCase, Driver
from selenium_stealth import stealth
import random
import uuid
import os
import shutil
from collections import defaultdict
from lxml import html
import base64
import hwid
import threading
from tenacity import retry, stop_after_attempt, wait_fixed
import logging
from chart_token_manager import generate_x_signature as central_generate_x_signature
from token_retrieval import get_hold_token as new_get_hold_token, get_cached_event_id, cache_event_id

logger = logging.getLogger("webook_pro")

# Seatsio IP configuration
SEATSIO_IP = "*************"
SEATSIO_DOMAINS = [
    "cdn-eu.seatsio.net",
    "messaging-eu.seatsio.net",
    "seatsio.net"
]

def replace_seatsio_domain_with_ip(url):
    """Replace seatsio domains with IP address for faster access"""
    original_url = url
    for domain in SEATSIO_DOMAINS:
        if domain in url:
            url = url.replace(domain, SEATSIO_IP)
            logger.debug(f"Replaced {domain} with {SEATSIO_IP} in URL: {original_url} -> {url}")
            break
    return url

def add_host_header_for_seatsio(headers, url):
    """Add Host header when using seatsio IP"""
    if headers is None:
        headers = {}

    # Check if URL contains seatsio IP and Host header doesn't already exist
    if SEATSIO_IP in url and 'Host' not in headers and 'host' not in headers:
        # Determine which domain to use as host
        for domain in SEATSIO_DOMAINS:
            if "cdn-eu" in url or "system/public" in url:
                headers['Host'] = 'cdn-eu.seatsio.net'
                break
        else:
            # Default to cdn-eu
            headers['Host'] = 'cdn-eu.seatsio.net'

        logger.debug(f"Added Host header: {headers.get('Host')} for IP URL")

    return headers

# --- Centralized HTTP Client & Request Management ---

_client_manager = {}
_client_lock = threading.Lock()

class UnifiedResponse:
    """
    A unified response object that mimics requests.Response,
    built from an httpx.Response or an error.
    """
    def __init__(self, response: httpx.Response = None, error: Exception = None):
        if response is not None:
            self.status_code = response.status_code
            self.headers = response.headers
            self.text = response.text
            self._content = response.content
            self.ok = response.is_success
            self.reason = response.reason_phrase
            self._response = response
        else:
            self.status_code = 0
            self.headers = {}
            self.text = ''
            self._content = b''
            self.ok = False
            self.reason = str(error) if error else "Unknown Error"
            self._response = None

    @property
    def content(self):
        """Return raw content bytes."""
        return self._content

    def json(self):
        """Parse response as JSON."""
        try:
            return json.loads(self.text)
        except json.JSONDecodeError:
            logger.error(f"Failed to decode JSON from response. Status: {self.status_code}, Text: '{self.text[:100]}...'")
            raise

def _parse_proxy_config(proxy: str | None) -> httpx.Proxy | None:
    """Parses a proxy string into an httpx-compatible format."""
    if not proxy:
        return None

    # Handle formats like '*********************:port'
    if '://' in proxy:
        return httpx.Proxy(url=proxy)

    # Handle formats like 'host:port:user:pass' or 'host:port'
    parts = proxy.split(":")
    if len(parts) == 4:
        host, port, user, password = parts
        proxy_url = f"http://{user}:{password}@{host}:{port}"
    elif len(parts) == 2:
        host, port = parts
        proxy_url = f"http://{host}:{port}"
    elif len(parts) == 3:
        # format is user:pass@host:port
        user, password, port = parts
        password, host = password.split("@")
        proxy_url = f"http://{user}:{password}@{host}:{port}"
    else:
        logger.warning(f"Unsupported proxy format: {proxy}. Ignoring proxy.")
        return None

    return httpx.Proxy(url=proxy_url)

def get_http_client(proxy: str | None = None, **kwargs) -> httpx.Client:
    """
    Retrieves a thread-safe, pooled httpx.Client for a given proxy config.
    Accepts extra httpx.Client kwargs for special cases.
    """
    proxy_config = _parse_proxy_config(proxy)
    proxy_key = str(proxy_config.url) if proxy_config else "no_proxy"

    # Create a stable string representation of kwargs to handle non-serializable objects
    kwargs_parts = [f"{key}:{str(value)}" for key, value in sorted(kwargs.items())]
    kwargs_key = "-".join(kwargs_parts)
    client_key = f"{proxy_key}-{kwargs_key}"

    with _client_lock:
        if client_key not in _client_manager:
            logger.debug(f"Creating new httpx client for key: {client_key}")
            _client_manager[client_key] = httpx.Client(proxy=proxy_config, **kwargs)
        return _client_manager[client_key]

def make_request(method: str, url: str, proxy: str | None = None, **kwargs) -> UnifiedResponse:
    """
    The single, centralized function for all HTTP requests.
    """
    # Replace seatsio domains with IP for faster access
    original_url = url
    url = replace_seatsio_domain_with_ip(url)

    # Extract special httpx settings if they exist
    httpx_settings = kwargs.pop('httpx_settings', {})

    # Disable SSL verification for seatsio IP connections
    if SEATSIO_IP in url:
        httpx_settings['verify'] = False
        logger.debug("Disabled SSL verification for seatsio IP connection")

    client = get_http_client(proxy, **httpx_settings)

    #403 headers
    # x-forwarded-for
    # x-forwarded
    # cf-connecting-ip
    # true-client-ip
    headers = {
        'x-forwarded-for': 'localhost',
        'x-forwarded': 'localhost',
        # 'cf-connecting-ip': 'localhost',
        # 'true-client-ip': 'localhost',
    }

    # Add Host header for seatsio IP connections
    if kwargs.get('headers'):
        kwargs['headers'] = add_host_header_for_seatsio(kwargs['headers'], url)
        for header in headers:
            if header not in kwargs['headers']:
                kwargs['headers'][header] = headers[header]
    else:
        kwargs['headers'] = add_host_header_for_seatsio(headers, url)

    try:
        response = client.request(method, url, **kwargs)
        # Cloudflare or other protections might return a 403.
        # This is handled as a non-successful but valid response.
        return UnifiedResponse(response=response)
    except httpx.RequestError as e:
        logger.error(f"Request failed: {method} {url} | Error: {e}")
        return UnifiedResponse(error=e)
    except Exception as e:
        logger.error(f"An unexpected error occurred during request: {method} {url} | Error: {e}")
        return UnifiedResponse(error=e)

# --- End Centralized HTTP Client & Request Management ---


def get_random_useragent(file_path='useragents.json'):
    if not hasattr(get_random_useragent, "useragents"):
        try:
            url = "https://useragents.me/"
            headers = {
                'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                'accept-language': 'en-US,en;q=0.9',
                'cache-control': 'no-cache',
                'pragma': 'no-cache',
                'referer': 'https://www.google.com/',
                'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36 Edg/131.0.0.0',
            }
            response = make_request('GET', url, timeout=5, headers=headers)
            if not response.ok:
                 raise Exception(f"Failed to fetch from useragents.me, status {response.status_code}")

            logger.debug('Got user agents successfully')
            tree = html.fromstring(response.content)
            xpath_expression = '//div[@id="most-common-desktop-useragents-json-csv"]/div[h3[text()="JSON"]]/textarea'
            json_text = tree.xpath(xpath_expression)[0].text
            get_random_useragent.useragents = json.loads(json_text)
            with open(file_path, 'w') as f:
                json.dump(get_random_useragent.useragents, f)
            logger.debug(f'User agents saved to {file_path}')

        except Exception as e:
            logger.error(f'Failed to fetch user agents online. Error: {e}')
            if os.path.exists(file_path):
                with open(file_path, 'r') as f:
                    get_random_useragent.useragents = json.load(f)
                logger.info(f'User agents loaded from {file_path}')
            else:
                raise RuntimeError(f"No user agents available online or offline at {file_path}")

    while True:
        yield random.choice(get_random_useragent.useragents)

user_agents = get_random_useragent()
next(user_agents)

def get_profile_info(bearer: str) -> dict:
    headers = {
        'accept': 'application/json',
        'authorization': f'Bearer {bearer}',
        'origin': 'https://webook.com',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36 Edg/131.0.0.0',
    }
    params = {'lang': 'ar'}
    url = 'https://api.webook.com/api/v2/user/profile'
    response = make_request('GET', url, params=params, headers=headers)
    return response.json() if response.ok else {}


def initialize_driver(browser_args={}):
    random_resolution_list = [
        '800x600', '1024x768', '1280x720', '1366x768', '1600x900', '1920x1080',
        '1536x864', '1440x900', '1280x800', '2560x1440', '2560x1600', '1680x1050'
    ]
    random_resolution = random.choice(random_resolution_list)
    d_width, d_height = random_resolution.split('x')
    browser_args['incognito'] = False
    browser_args['multi_proxy'] = True
    chromium_args = [
        "--disable-plugins", "--disable-popup-blocking", "--disable-infobars",
        "--disable-notifications", "--disable-geolocation", "--disable-remote-fonts",
        "--disable-background-networking", "--disable-background-timer-throttling",
        "--disable-backgrounding-occluded-windows", "--disable-renderer-backgrounding",
        "--disable-web-security", "--disable-client-side-phishing-detection",
        "--disable-component-update", "--disable-default-apps", "--disable-domain-reliability",
        "--disable-features=site-per-process", "--disable-hang-monitor",
        "--disable-ipc-flooding-protection", "--disable-prompt-on-repost", "--disable-sync",
        "--disable-translate", "--disable-web-resources", "--disable-webgl",
        "--disable-webrtc", "--disable-xss-auditor", "--no-sandbox",
        "--ignore-certificate-errors", "--disable-dev-shm-usage", "--disable-gpu",
        "--disable-software-rasterizer", "--force-color-profile=srgb",
        f"--window-size={d_width},{d_height}",
    ]

    selected_args = random.sample(chromium_args, k=random.randint(5, len(chromium_args)))
    browser_args['chromium_arg'] = ",".join(selected_args)

    driver = Driver(**browser_args)

    languages_options = [["en-US", "en"], ["fr-FR", "fr"], ["es-ES", "es"], ["de-DE", "de"], ["ar-SA", "ar"]]
    vendor_options = ["Google Inc.", "Apple Inc.", "Microsoft Corporation", "Mozilla Foundation"]
    platform_options = ["Win32", "Win64", "MacIntel", "Linux x86_64"]
    webgl_vendor_options = ["Intel Inc.", "NVIDIA Corporation", "AMD Inc.", "Qualcomm", "ARM"]
    renderer_options = ["Intel Iris OpenGL Engine", "AMD Radeon Pro", "NVIDIA GeForce GTX", "ANGLE (Intel, Intel(R) HD Graphics)"]
    fix_hairline_options = [True, False]

    stealth(
        driver,
        languages=random.choice(languages_options),
        vendor=random.choice(vendor_options),
        platform=random.choice(platform_options),
        webgl_vendor=random.choice(webgl_vendor_options),
        renderer=random.choice(renderer_options),
        user_agent=next(user_agents)['ua'],
        run_on_insecure_origins=True,
        fix_hairline=random.choice(fix_hairline_options)
    )
    # In a real scenario, you'd return some debug_info. For now, it's omitted.
    return driver, {}


def build_channel_keys(channel_keys, team_id):
    if not channel_keys or channel_keys == ['NO_CHANNEL']:
        return ['NO_CHANNEL']

    if not hasattr(build_channel_keys, '_cache'):
        build_channel_keys._cache = {}

    cache_key = (json.dumps(channel_keys, sort_keys=True), team_id)
    if cache_key in build_channel_keys._cache:
        return build_channel_keys._cache[cache_key]

    keys = []
    if isinstance(channel_keys, dict):
        if 'common' in channel_keys and channel_keys['common']:
            keys.extend(['NO_CHANNEL', channel_keys['common'][0]])
            if team_id and team_id in channel_keys and channel_keys[team_id]:
                keys.append(channel_keys[team_id][0])
            else:
                for k, v in channel_keys.items():
                    if k != 'common' and v:
                        keys.append(v[0])
                        break

    if not keys:
        keys.append('NO_CHANNEL')
        if team_id and isinstance(channel_keys, dict) and team_id in channel_keys and channel_keys[team_id]:
            keys.append(channel_keys[team_id][0])

    build_channel_keys._cache[cache_key] = keys
    return keys


def switch_seat_immediate(
    seat_number: str, event_key: str, old_token: str, new_token: str,
    channel_keys=None, team_id=None, proxy: str = None, max_attempts=2
) -> bool:
    total_start_time = time.time()
    thread_name = threading.current_thread().name
    threading.current_thread().name = f"SeatSwitch-{seat_number}"

    shared_browser_id = ''.join(f"{random.randint(0, 0xFFFF):04x}" for _ in range(4))
    release_json = {'events': [event_key], 'holdToken': old_token, 'objects': [{'objectId': seat_number}], 'validateEventsLinkedToSameChart': True}
    release_body = json.dumps(release_json, separators=(',', ':'))
    hold_keys = build_channel_keys(channel_keys, team_id)
    hold_json = {'events': [event_key], 'holdToken': new_token, 'objects': [{'objectId': seat_number}], 'channelKeys': hold_keys, 'validateEventsLinkedToSameChart': True}
    hold_body = json.dumps(hold_json, separators=(',', ':'))

    release_headers = {'accept': '*/*', 'content-type': 'application/json', 'origin': 'https://cdn-eu.seatsio.net', 'priority': 'u=3', 'x-browser-id': shared_browser_id, 'x-client-tool': 'Renderer', 'x-signature': generate_x_signature(release_body)}
    hold_headers = {'accept': '*/*', 'content-type': 'application/json', 'origin': 'https://cdn-eu.seatsio.net', 'priority': 'u=1', 'x-browser-id': shared_browser_id, 'x-client-tool': 'Renderer', 'x-signature': generate_x_signature(hold_body)}

    base_url = 'https://*************/system/public/3d443a0c-83b8-4a11-8c57-3db9d116ef76/events/groups/actions/'
    release_url = f"{base_url}release-held-objects"
    hold_url = f"{base_url}hold-objects"

    FATAL_ERROR_PATTERNS = ["not in status reservedByToken", "no longer available", "has already been reserved", "token is not valid", "token has expired"]

    success = False
    
    # Define specialized client settings for this high-performance function
    transport = httpx.HTTPTransport(http2=True, retries=1, limits=httpx.Limits(max_connections=100, max_keepalive_connections=20, keepalive_expiry=30), verify=False)
    client_settings = {"transport": transport, "timeout": httpx.Timeout(0.5, read=0.5, write=0.5, pool=1.0), "http2": True}
    client = get_http_client(proxy, **client_settings)

    for attempt in range(1, max_attempts + 1):
        try:
            release_resp = client.post(release_url, headers=release_headers, content=release_body, timeout=0.75)
            if release_resp.status_code != 204:
                if any(p in release_resp.text for p in FATAL_ERROR_PATTERNS):
                    logger.warning(f"[SWITCH] Fatal error releasing seat {seat_number}: {release_resp.text}")
                    break
                continue

            hold_req = client.build_request("POST", hold_url, headers=hold_headers, content=hold_body)
            hold_resp = client.send(hold_req)

            if hold_resp.status_code == 204:
                success = True
                break
            if "has already been reserved" in hold_resp.text:
                logger.warning(f"[SWITCH] Seat {seat_number} was sniped during switch.")
                break
        except (httpx.RequestError, httpx.TimeoutException) as e:
            if attempt == 1: logger.warning(f"[SWITCH] Network error for {seat_number}: {str(e)[:100]}")
            continue
        except Exception as e:
            if attempt == 1: logger.warning(f"[SWITCH] Unexpected error for {seat_number}: {str(e)[:100]}")
            continue

    total_time = time.time() - total_start_time
    threading.current_thread().name = thread_name
    if success:
        logger.info(f"✅ SWITCHED seat {seat_number} in {total_time:.3f}s on attempt {attempt}")
    else:
        logger.error(f"❌ FAILED to switch seat {seat_number} after {total_time:.3f}s")
    return success


def get_webook_event_info(event_key: str, is_season: bool = False, proxy: str = None):
    headers = {
        'accept': 'application/json',
        'origin': 'https://webook.com',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36 Edg/135.0.0.0',
        'token': 'e9aac1f2f0b6c07d6be070ed14829de684264278359148d6a582ca65a50934d2',
    }
    params = {'lang': 'en', 'visible_in': 'rs'}
    path = f'season-detail/{event_key}' if is_season else f'event-detail/{event_key}'
    url = f'https://api.webook.com/api/v2/{path}'

    response = make_request('GET', url, headers=headers, params=params, proxy=proxy, timeout=10)

    if response.ok:
        try:
            cache_event_id(response.json())
        except Exception as e:
            logger.error(f"Error caching event ID: {e}")
    return response

def _get_seatsio_common_headers():
    return {
        'accept': '*/*',
        'origin': 'https://cdn-eu.seatsio.net',
        'referer': 'https://cdn-eu.seatsio.net/static/version/seatsio-ui-prod-00221-fff/chart-renderer/chartRendererIframe.html',
        'user-agent': 'Mozilla/5.0 (Linux; Android 4.4.2; Nexus 4 Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/34.0.1847.114 Mobile Safari/537.36',
        'x-browser-id': ''.join(f"{random.randint(0, 0xFFFF):04x}" for _ in range(4)),
        'x-client-tool': 'Renderer',
        'x-signature': generate_x_signature(""),
    }

def get_match_info(chart_key: str, drawVersion: int, proxy: str = None) -> dict:
    url = f'https://cdn-eu.seatsio.net/system/public/3d443a0c-83b8-4a11-8c57-3db9d116ef76/charts/{chart_key}/published/{drawVersion}'
    response = make_request('GET', url, headers=_get_seatsio_common_headers(), proxy=proxy, timeout=10)

    if response.ok:
        match_data_bytes = VM.deobfuscate(response.content, chart_key)
        return json.loads(match_data_bytes)
    else:
        logger.error(f"Failed to get match info for {chart_key}. Status: {response.status_code}")
        return {'Error': response.status_code}

def get_object_statuses(event_key: str, chart_key: str, proxy: str = None) -> list | dict:
    url = 'https://cdn-eu.seatsio.net/system/public/3d443a0c-83b8-4a11-8c57-3db9d116ef76/events/object-statuses'
    params = {'event_key': event_key}
    response = make_request('GET', url, params=params, headers=_get_seatsio_common_headers(), proxy=proxy, timeout=10)

    if response.ok:
        objects_bytes = VM.deobfuscate(response.content, chart_key)
        objects = json.loads(objects_bytes)
        return sorted(objects, key=lambda x: x.get('objectLabelOrUuid', ''))
    else:
        logger.error(f"Failed to get object statuses for {event_key}. Status: {response.status_code}")
        return {'Error': response.status_code}

def get_event_render_data(event_key:str, proxy: str = None) -> dict:
    url = 'https://*************/system/public/3d443a0c-83b8-4a11-8c57-3db9d116ef76/rendering-info'
    params = {'event_key': event_key}
    headers = _get_seatsio_common_headers()
    headers['x-request-origin'] = 'webook.com'
    response = make_request('GET', url, params=params, headers=headers, proxy=proxy, timeout=10)
    return response.json() if response.ok else {'Error': response.status_code}

def get_event_seatsio_info(seatsio_data:dict, proxy: str = None) -> dict:
    chart_key = seatsio_data['chart_key']
    event_key = seatsio_data.get('season_key') or seatsio_data['event_key']

    render_data = get_event_render_data(event_key, proxy=proxy)
    if 'Error' in render_data:
        logger.critical(f"Error getting event render data: {render_data['Error']}")
        return render_data

    match_data = get_match_info(chart_key, render_data['drawingVersion'], proxy=proxy)
    if 'Error' in match_data:
        logger.critical(f"Error getting match info: {match_data['Error']}")
        return match_data

    match_data['season_info'] = render_data.get('seasonStructure')
    return match_data

def get_hold_token(proxy=None):
    event_id = get_cached_event_id()
    if not event_id:
        logger.error("No event ID cached. Please load an event first.")
        return None
    return new_get_hold_token(event_id=event_id, proxy=proxy)

def activate_hold_token(token, proxy=None):
    url = f'https://cdn-eu.seatsio.net/system/public/3d443a0c-83b8-4a11-8c57-3db9d116ef76/hold-tokens/{token}'
    headers = _get_seatsio_common_headers()
    headers['x-signature'] = generate_x_signature("") # Re-sign for this specific call
    response = make_request('GET', url, headers=headers, proxy=proxy, timeout=5)
    return response.json().get('expiresInSeconds', 590) if response.ok else 590

def test_proxy(proxy_url, target_url="https://www.google.com", timeout=5):
    response = make_request('GET', target_url, proxy=proxy_url, timeout=timeout)
    return {
        "proxy": proxy_url,
        "status": response.ok,
        "status_code": response.status_code,
        "error": None if response.ok else response.reason
    }

def hold_seat(seat_number, event, hold_token, channel_keys=['NO_CHANNEL',], team_id=None, proxy=None):
    if not hold_token:
        hold_token = get_hold_token(proxy)
        if not hold_token:
            logger.error("Failed to acquire a hold token.")
            return False

    keys = build_channel_keys(channel_keys, team_id)
    json_data = {
        'events': [event],
        'holdToken': hold_token,
        'objects': [{'objectId': seat_number}],
        'channelKeys': keys,
        'validateEventsLinkedToSameChart': True,
    }
    body_str = json.dumps(json_data, separators=(',', ':'))
    headers = _get_seatsio_common_headers()
    headers.update({
        'content-type': 'application/json',
        'x-signature': generate_x_signature(body_str)
    })
    url = 'https://cdn-eu.seatsio.net/system/public/3d443a0c-83b8-4a11-8c57-3db9d116ef76/events/groups/actions/hold-objects'
    response = make_request('POST', url, content=body_str, headers=headers, proxy=proxy)

    if response.status_code == 204:
        logger.info(f'Reserved seat {seat_number} with hold token {hold_token}')
        return True
    else:
        logger.error(f'Error holding seat {seat_number}: Status {response.status_code} - {response.text}')
        return False

def release_seat(seat_number, event, hold_token):
    json_data = {
        'events': [event],
        'holdToken': hold_token,
        'objects': [{'objectId': seat_number}],
        'validateEventsLinkedToSameChart': True,
    }
    body_str = json.dumps(json_data, separators=(',', ':'))
    headers = _get_seatsio_common_headers()
    headers.update({
        'content-type': 'application/json',
        'x-signature': generate_x_signature(body_str)
    })
    url = 'https://cdn-eu.seatsio.net/system/public/3d443a0c-83b8-4a11-8c57-3db9d116ef76/events/groups/actions/release-held-objects'
    response = make_request('POST', url, content=body_str, headers=headers)

    if response.status_code == 204:
        logger.info(f'Released seat {seat_number} with hold token {hold_token}')
        return True
    else:
        logger.error(f'Error releasing seat {seat_number}: Status {response.status_code} - {response.text}')
        return False

# --- Utility & Processing Functions (Largely Unchanged) ---

class VM:
    @staticmethod
    def deobfuscate(e, t):
        i = 63 & VM.hash_code(t)
        n = bytearray(e)
        for j in range(len(n)):
            n[j] = (n[j] - i) & 0xFF
        return n.decode('utf-8')

    @staticmethod
    def hash_code(e):
        t = 0
        for i in range(len(e)):
            t = (29 * t % 10007 + ord(e[i])) % 10007
        return t

def generate_x_signature(body: str) -> str:
    return central_generate_x_signature(body)

def group_tickets_by_type_and_status(data, free_only=False):
    ticket_groups = defaultdict(lambda: defaultdict(dict))
    for entry in data:
        seat_id = entry.get('objectLabelOrUuid')
        if not seat_id:
            continue
        status = entry.get('status', 'free').lower()
        if free_only and status != 'free':
            continue
        ticket_type = seat_id.split('-')[0].strip() if '-' in seat_id else seat_id
        if not ticket_type: ticket_type = "UNKNOWN"
        ticket_groups[ticket_type][status][seat_id] = entry
    return ticket_groups

if __name__ == "__main__":
    # Example Usage
    logging.basicConfig(level=logging.INFO)
    logger.info("Running main execution block...")

    # Test a proxy
    proxy_test_result = test_proxy(proxy_url='http://taplmftg-rotate:<EMAIL>:80')
    logger.info(f"Proxy test result: {proxy_test_result}")