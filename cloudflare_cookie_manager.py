# simple_cloudflare_cookies.py - Simple Cloudflare cookie storage

import threading
import logging

logger = logging.getLogger('webook_pro')

class CloudflareCookies:
    """Simple storage for Cloudflare cookies with get/set methods"""
    
    def __init__(self):
        self._cf_bm = None
        self._cfuvid = None
        self._lock = threading.Lock()
    
    def set_cookies(self, cf_bm=None, cfuvid=None):
        """
        Set Cloudflare cookies if they are currently None
        
        Args:
            cf_bm: __cf_bm cookie value
            cfuvid: _cfuvid cookie value
        """
        with self._lock:
            if cf_bm and self._cf_bm is None:
                self._cf_bm = cf_bm
                logger.info("Set __cf_bm cookie")
            
            if cfuvid and self._cfuvid is None:
                self._cfuvid = cfuvid
                logger.info("Set _cfuvid cookie")
    
    def get_cookies(self):
        """
        Get current Cloudflare cookies
        
        Returns:
            dict: Dictionary with cookie names and values, only includes non-None cookies
        """
        with self._lock:
            cookies = {}
            if self._cf_bm:
                cookies['__cf_bm'] = self._cf_bm
            if self._cfuvid:
                cookies['_cfuvid'] = self._cfuvid
            return cookies
    
    def get_cookie_header(self):
        """
        Get formatted cookie header string
        
        Returns:
            str: Cookie header string or empty string if no cookies
        """
        cookies = self.get_cookies()
        if not cookies:
            return ""
        
        cookie_parts = []
        for name, value in cookies.items():
            cookie_parts.append(f"{name}={value}")
        
        return "; ".join(cookie_parts)
    
    def clear_cookies(self):
        """Clear all cookies (set to None when not working)"""
        with self._lock:
            self._cf_bm = None
            self._cfuvid = None
            logger.info("Cleared all Cloudflare cookies")
    
    def clear_cf_bm(self):
        """Clear only __cf_bm cookie"""
        with self._lock:
            self._cf_bm = None
            logger.info("Cleared __cf_bm cookie")
    
    def clear_cfuvid(self):
        """Clear only _cfuvid cookie"""
        with self._lock:
            self._cfuvid = None
            logger.info("Cleared _cfuvid cookie")
    
    def has_cookies(self):
        """Check if any cookies are set"""
        with self._lock:
            return self._cf_bm is not None or self._cfuvid is not None
    
    def get_status(self):
        """Get current cookie status"""
        with self._lock:
            return {
                '__cf_bm': self._cf_bm is not None,
                '_cfuvid': self._cfuvid is not None,
                'has_cookies': self._cf_bm is not None or self._cfuvid is not None
            }

# Global instance
_global_cf_cookies = CloudflareCookies()

def get_cf_cookies():
    """Get the global CloudflareCookies instance"""
    return _global_cf_cookies

def set_cf_cookies(cf_bm=None, cfuvid=None):
    """Set Cloudflare cookies globally"""
    _global_cf_cookies.set_cookies(cf_bm, cfuvid)

def get_cf_cookie_header():
    """Get formatted cookie header for requests"""
    return _global_cf_cookies.get_cookie_header()

def clear_cf_cookies():
    """Clear all Cloudflare cookies"""
    _global_cf_cookies.clear_cookies()

def extract_cookies_from_response(response):
    """
    Extract Cloudflare cookies from http.client response and store them
    
    Args:
        response: http.client response object
    """
    try:
        # Get Set-Cookie headers
        if hasattr(response, 'getheaders'):
            set_cookie_headers = response.getheaders('Set-Cookie')
        else:
            return
        
        cf_bm = None
        cfuvid = None
        
        # Parse each Set-Cookie header
        for header in set_cookie_headers:
            if '__cf_bm=' in header:
                # Extract __cf_bm value
                parts = header.split(';')[0]  # Get just the name=value part
                if '=' in parts:
                    cf_bm = parts.split('=', 1)[1]
            
            elif '_cfuvid=' in header:
                # Extract _cfuvid value
                parts = header.split(';')[0]  # Get just the name=value part
                if '=' in parts:
                    cfuvid = parts.split('=', 1)[1]
        
        # Set cookies if found
        if cf_bm or cfuvid:
            set_cf_cookies(cf_bm, cfuvid)
            logger.info(f"Extracted cookies from response - cf_bm: {'Yes' if cf_bm else 'No'}, cfuvid: {'Yes' if cfuvid else 'No'}")
    
    except Exception as e:
        logger.error(f"Error extracting cookies from response: {str(e)}")

def add_cookies_to_request(headers):
    """
    Add Cloudflare cookies to request headers if available
    
    Args:
        headers: Dictionary of headers to modify
        
    Returns:
        dict: Updated headers dictionary
    """
    cookie_header = get_cf_cookie_header()
    if cookie_header:
        headers = headers.copy() if headers else {}
        
        # Add to existing Cookie header or create new one
        if 'Cookie' in headers:
            headers['Cookie'] = f"{headers['Cookie']}; {cookie_header}"
        else:
            headers['Cookie'] = cookie_header
        
        logger.debug(f"Added Cloudflare cookies to request: {cookie_header}")
    
    return headers