# worker/booking_worker.py

import time
import random
import logging
import threading
from PyQt5.QtCore import QThread, pyqtSignal, QObject
from collections import defaultdict
import asyncio
import concurrent.futures
import json
from helper import get_hold_token, get_random_useragent, hold_seat, build_channel_keys, UnifiedResponse, make_request
from fast_hold import get_chart_token, generate_x_signature_local
import requests


logger = logging.getLogger("webook_pro")
user_agents = get_random_useragent()
next(user_agents)
# Maximum seats per token
MAX_SEATS_PER_TOKEN = 5

class BookingProgressSignals(QObject):
    """Signals for progress reporting during booking"""
    started = pyqtSignal(int)  # total seats to book
    progress = pyqtSignal(int, int)  # current, total
    seat_held = pyqtSignal(str, str)  # seat_id, token_id
    token_created = pyqtSignal(str)  # token_id
    error = pyqtSignal(str)  # error message
    finished = pyqtSignal()  # booking completed

class BookingWorker(QThread):
    # Change the signal to emit a dictionary with booking stats instead of token managers
    finished_signal = pyqtSignal(dict)  # Emits booking stats including booked seat count
    log_signal = pyqtSignal(str)
    proxy_result_signal = pyqtSignal(str, bool, float)
    progress_signals = None  # Initialized in __init__

    def __init__(
        self,
        event_key,
        chart_key,
        channel_keys,
        desired_count,
        free_seats_by_type,
        event_id=None,
        team_id=None,
        name="BookingWorker",
        proxies=None
    ):
        super().__init__()
        self.setObjectName(name)

        self.event_id = event_id
        self.event_key = event_key
        self.chart_key = chart_key
        self.channel_keys = channel_keys
        self.desired_count = desired_count
        self.free_seats_by_type = free_seats_by_type
        self.team_id = team_id
        self.proxies = proxies if proxies else []
        self.progress_signals = BookingProgressSignals()

        # Track tokens and seats (not TokenManager instances)
        self.booked_seats = {}  # token_id -> [seat_ids]
        
        # For cancellation
        self._running = True
        
        # Task executor
        self.executor = concurrent.futures.ThreadPoolExecutor(
            max_workers=5,
            thread_name_prefix=f"Booking-{name}"
        )

        self.booking_counter = 0
        self.session_rotation_lock = threading.Lock()
        self.sessions = {}  # Thread ID -> session
        self.force_rotation = False

    def run(self):
        logger.debug(f"[{self.objectName()}] Thread run started.")
        
        try:
            # Signal booking start
            self.progress_signals.started.emit(self.desired_count)
            
            # Create list of all available seats
            all_seats = []
            for ttype, seats_list in self.free_seats_by_type.items():
                all_seats.extend(seats_list)
                
            # Shuffle seats for randomness
            random.shuffle(all_seats)
            
            # Calculate how many tokens we need (limit of 5 seats per token)
            token_count = (self.desired_count + MAX_SEATS_PER_TOKEN - 1) // MAX_SEATS_PER_TOKEN
            # Create tokens concurrently using ThreadPoolExecutor
            self.log_signal.emit(f"🔑 Creating {token_count} tokens in parallel...")
            tokens = []

            # Submit all token creation tasks to the executor
            futures = []
            for i in range(token_count):
                if not self._running:
                    self.log_signal.emit("🛑 Booking cancelled")
                    break
                    
                # Submit the token creation task
                future = self.executor.submit(get_hold_token, self._pick_random_proxy())
                futures.append(future)

            # Process tokens as they complete
            completed = 0
            for future in concurrent.futures.as_completed(futures):
                if not self._running:
                    self.log_signal.emit("🛑 Booking cancelled")
                    break
                    
                completed += 1
                try:
                    token = future.result()
                    if token:
                        tokens.append(token)
                        self.progress_signals.token_created.emit(token)
                        self.log_signal.emit(f"🔑 Created new token {completed}/{token_count}: {token} (max {MAX_SEATS_PER_TOKEN} seats per token)")
                    else:
                        self.log_signal.emit(f"❌ Failed to create token {completed}/{token_count}")
                except Exception as e:
                    self.log_signal.emit(f"❌ Error creating token {completed}/{token_count}: {str(e)}")

            # Log summary after all tokens are processed
            self.log_signal.emit(f"✅ Created {len(tokens)}/{token_count} tokens")
                   
            if len(tokens) < token_count:
                self.log_signal.emit(f"⚠️ Warning: Could only create {len(tokens)}/{token_count} tokens")
                
            # Book seats using all created tokens
            booked_count = 0
            total_seats = min(self.desired_count, len(all_seats))
            
            self.log_signal.emit(f"🎟️ Starting to book {total_seats} seats...")
            
            # Process seats in parallel
            futures_list = []  # Explicitly use a list for futures
            for seat_index in range(total_seats):
                if not self._running:
                    self.log_signal.emit("🛑 Booking cancelled")
                    break
                    
                # Determine which token to use for this seat
                token_index = seat_index // MAX_SEATS_PER_TOKEN
                if token_index >= len(tokens):
                    self.log_signal.emit(f"⚠️ No token available for seat {seat_index+1}")
                    continue
                    
                token = tokens[token_index]
                seat_data = all_seats[seat_index]
                seat_label = seat_data["objectLabelOrUuid"]
                
                # Submit seat booking task
                future = self.executor.submit(
                    self._hold_seat_task,
                    seat_label,
                    token,
                    seat_index,
                    total_seats
                )
                futures_list.append(future)
                
                # Limit concurrency to 3 active requests
                if len(futures_list) >= 3:
                    # Wait for at least one to complete
                    done_futures, pending_futures = concurrent.futures.wait(
                        futures_list, 
                        return_when=concurrent.futures.FIRST_COMPLETED
                    )
                    
                    # Replace futures_list with the pending futures
                    futures_list = list(pending_futures)
                    
                    # Process completed futures
                    for future in done_futures:
                        success, seat_id, token_id = future.result()
                        if success:
                            # Store in booked_seats instead of creating TokenManager
                            if token_id not in self.booked_seats:
                                self.booked_seats[token_id] = []
                            self.booked_seats[token_id].append(seat_id)
                            booked_count += 1
            
            # Wait for remaining futures
            if futures_list:
                self.log_signal.emit(f"⏳ Waiting for {len(futures_list)} pending seat requests to complete...")
                
            for future in concurrent.futures.as_completed(futures_list):
                success, seat_id, token_id = future.result()
                if success:
                    # Store in booked_seats instead of creating TokenManager
                    if token_id not in self.booked_seats:
                        self.booked_seats[token_id] = []
                    self.booked_seats[token_id].append(seat_id)
                    booked_count += 1
            
            # Signal completion
            self.progress_signals.finished.emit()
            self.log_signal.emit(f"🎉 Booking complete! Booked {booked_count}/{self.desired_count} seats using {len(self.booked_seats)} tokens")
            
            # Instead of returning token managers, return booking statistics
            booking_results = {
                "success": True,
                "booked_count": booked_count,
                "token_count": len(self.booked_seats),
                "booked_seats": self.booked_seats,  # token_id -> [seat_ids]
                "total_desired": self.desired_count
            }
            
            self.finished_signal.emit(booking_results)
                
        except Exception as e:
            logger.exception(f"[{self.objectName()}] Booking failed: {e}")
            self.log_signal.emit(f"❌ [BookingWorker] Error: {str(e)}")
            self.progress_signals.error.emit(str(e))
            self.finished_signal.emit({"success": False, "error": str(e)})
            
        finally:
            # Clean up
            self.executor.shutdown(wait=False)
            logger.debug(f"[{self.objectName()}] Thread run finished.")

    def _hold_seat_task(self, seat_label, token, seat_index, total_seats):
        """Task function to hold a seat with effective proxy rotation"""
        success = False
        start_time = time.time()
        proxy = self._pick_random_proxy()

        # Check if we need to rotate proxy
        with self.session_rotation_lock:
            self.booking_counter += 1
            should_rotate = (self.booking_counter % 50 == 0)
            if should_rotate:
                self.log_signal.emit("🔄 Rotating proxy connection for new IP address")
                self.log_signal.emit("waiting 10 seconds for seatsio to load after 50 seats")
                time.sleep(10)
        
        try:
            # Prepare request data
            json_data = {
                'events': [self.event_key],
                'holdToken': token,
                'objects': [{'objectId': seat_label}],
                'channelKeys': build_channel_keys(self.channel_keys, self.team_id),
                'validateEventsLinkedToSameChart': True,
            }
            
            # Prepare body and signature
            body_str = json.dumps(json_data, separators=(',', ':'))
            x_sig = generate_x_signature_local(body_str)
            
            # Use optimized headers with randomized browser ID
            headers = {
                'accept': '*/*',
                'accept-language': 'en-US,en;q=0.9',
                'content-type': 'application/json',
                'origin': 'https://cdn-eu.seatsio.net',
                'host': 'cdn-eu.seatsio.net',  # Add Host header for IP connection
                'priority': 'u=1, i',
                'referer': 'https://cdn-eu.seatsio.net/static/version/seatsio-ui-prod-00221-fff/chart-renderer/chartRendererIframe.html',
                'user-agent': next(user_agents)['ua'],
                'x-browser-id': ''.join(f"{random.randint(0, 0xFFFF):04x}" for _ in range(4)),
                'x-client-tool': 'Renderer',
                'x-kl-saas-ajax-request': 'Ajax_Request',
                'x-signature': x_sig,
            }
            url = 'https://*************/system/public/3d443a0c-83b8-4a11-8c57-3db9d116ef76/events/groups/actions/hold-objects'
            # Make the request with explicit proxy settings for this request
            response: UnifiedResponse = make_request(
            method="POST",
            url=url,
            headers=headers,
            json=json_data,
            proxy=proxy,
        )

            
            # Calculate elapsed time
            elapsed_ms = (time.time() - start_time) * 1000
            
            # Process response
            if response.status_code == 204:
                success = True
                elapsed_ms = (time.time() - start_time) * 1000
                self.proxy_result_signal.emit(proxy, True, elapsed_ms)
                self.log_signal.emit(f"✅ [Token {token[:8]}] Held seat {seat_label} in {elapsed_ms:.2f}ms")
                self.progress_signals.seat_held.emit(seat_label, token)
            else:
                self.log_signal.emit(f"❌ [Token {token[:8]}] Failed to hold seat {seat_label}: {response.status_code} in {elapsed_ms:.2f}ms")
                self.proxy_result_signal.emit(proxy, False, elapsed_ms)

            # Update progress
            self.progress_signals.progress.emit(seat_index + 1, total_seats)
            
        except Exception as e:
            elapsed_ms = (time.time() - start_time) * 1000
            self.log_signal.emit(f"❗ Error holding seat {seat_label} in {elapsed_ms:.2f}ms: {str(e)}")
            
        return success, seat_label, token
    
    # Add helper method to format proxy dictionary correctly
    def _format_proxy_dict(self, proxy_string):
        """Format proxy string into correct dictionary format for requests"""
        if not proxy_string:
            return None
            
        proxies = None
        parts = proxy_string.split(":")
        
        try:
            if len(parts) == 4:  # host:port:user:pass format
                proxy_host, proxy_port, proxy_user, proxy_pass = parts
                proxies = {
                    "http": f"http://{proxy_user}:{proxy_pass}@{proxy_host}:{proxy_port}",
                    "https": f"http://{proxy_user}:{proxy_pass}@{proxy_host}:{proxy_port}",
                }
            elif len(parts) == 2:  # host:port format
                proxy_host, proxy_port = parts
                proxies = {
                    "http": f"http://{proxy_host}:{proxy_port}",
                    "https": f"http://{proxy_host}:{proxy_port}",
                }
        except Exception as e:
            self.log_signal.emit(f"Error formatting proxy: {str(e)}")
            return None
            
        return proxies


    def _create_token(self):
        """Create a new token and return its ID"""
        try:
            # Get a random proxy
            proxy = self._pick_random_proxy()
            
            # Get new hold token
            token = get_hold_token(proxy)
            if not token:
                self.log_signal.emit("Failed to get new token")
                return None            
            return token
            
        except Exception as e:
            self.log_signal.emit(f"Error creating token: {str(e)}")
            return None


    def _pick_random_proxy(self):
        """Returns a random proxy from the list, or None if none are available."""
        if not self.proxies:
            return None
        return random.choice(self.proxies)
        
    def stop(self):
        """Stop the booking process and clean up resources"""
        self._running = False
        
        # Clean up sessions
        for thread_id, session in list(self.sessions.items()):
            try:
                session.close()
            except:
                pass
        self.sessions.clear()
        
        self.log_signal.emit("Stopping booking process...")
