import asyncio
import json
import logging
import websockets
from PyQt5.QtCore import QThread, pyqtSignal, QObject, QTimer
import certifi
import ssl
import time


logger = logging.getLogger("webook_pro")
ssl_context = ssl.create_default_context(cafile=certifi.where())

class WebSocketManager(QObject):  # Inherit from QObject for signal/slot support
    seat_data_updated = pyqtSignal(dict)
    error_signal = pyqtSignal(str)
    connected_signal = pyqtSignal(bool)
    cleanup_trigger_signal = pyqtSignal()  # Signal for periodic cleanup

    def __init__(self, event_key: str, chart_key: str, parent=None):
        super().__init__(parent)
        self.event_key = event_key
        self.chart_key = chart_key
        self.websocket = None
        self._running = True
        # Updated URI for the new websocket service
        self.uri = (
            "wss://messaging-eu.seatsio.net/ws"
        )
        self.headers = {
            "Origin": "https://cdn-eu.seatsio.net",
            "Cache-Control": "no-cache",
            "Accept-Language": "en-US,en;q=0.9",
            "Pragma": "no-cache",
            "User-Agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 "
                          "(KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36"
        }
        # New format for channel names (without rewind parameter)
        self.channel_name = f"3d443a0c-83b8-4a11-8c57-3db9d116ef76"
        self.event_channel_name = f"3d443a0c-83b8-4a11-8c57-3db9d116ef76-{self.event_key}"
        self.loop = asyncio.new_event_loop()
        self.thread = None  # Store the QThread instance
        self.last_cleanup_time = time.time()
        self.cleanup_interval = 10  # Run cleanup every 10 seconds
        
        # New heartbeat monitoring
        self.last_message_time = time.time()
        self.heartbeat_timer = QTimer()
        self.heartbeat_timer.timeout.connect(self.check_heartbeat)
        self.heartbeat_timer.setInterval(30000)  # Check every 30 seconds
        self.max_silence_duration = 60  # Seconds without messages before force reconnect
        
        # New long-term reconnection timer
        self.reconnect_timer = QTimer()
        self.reconnect_timer.timeout.connect(self.force_reconnect)
        self.reconnect_timer.setInterval(600000)  # Force reconnect every 10 minutes

    def start(self):
        """Starts the WebSocket connection in a separate thread."""
        if self.thread is None:
            self.thread = QThread()
            self.moveToThread(self.thread)
            self.thread.started.connect(self._run_websocket)
            self.thread.start()
            logger.info("WebSocket manager thread started")
            
            # Start timers
            self.heartbeat_timer.start()
            self.reconnect_timer.start()

    def _run_websocket(self):
        """Internal method to run the WebSocket connection and loop."""
        asyncio.set_event_loop(self.loop)
        self.loop.run_until_complete(self._listen_to_channel())

    def check_heartbeat(self):
        """Check if we've received messages recently, force reconnect if not"""
        current_time = time.time()
        silence_duration = current_time - self.last_message_time
        
        if silence_duration > self.max_silence_duration:
            logger.warning(f"No messages received for {silence_duration:.1f} seconds, forcing reconnection...")
            self.force_reconnect()
    
    def force_reconnect(self):
        """Force a reconnection of the websocket"""
        logger.info("Performing scheduled reconnection to refresh websocket connection")
        
        # First emit disconnected signal
        self.connected_signal.emit(False)
        
        # Close existing websocket if it exists
        if self.websocket:
            # Use run_coroutine_threadsafe to schedule the coroutine in the correct loop
            asyncio.run_coroutine_threadsafe(self.websocket.close(), self.loop)
            
        # Reset last message time to prevent immediate reconnect
        self.last_message_time = time.time()

    async def _listen_to_channel(self):
        """Connects to websocket, subscribes, and processes messages."""
        max_retries = 5  # Increased retries
        retry_count = 0
        base_delay = 1

        while self._running:
            try:
                # Log connection attempt
                logger.info(f"Connecting to websocket (attempt {retry_count + 1})...")

                async with websockets.connect(
                        self.uri,
                        user_agent_header=self.headers.pop("User-Agent", "Mozilla/5.0"),
                        additional_headers=self.headers,
                        logger=None,  # Disable websockets internal logging
                        ping_interval=15,
                        ping_timeout=5,
                        ssl=ssl_context,
                        close_timeout=5  # Added close timeout
                    ) as self.websocket:

                    # Reset retry counter on successful connection
                    retry_count = 0
                    self.last_message_time = time.time()  # Reset heartbeat timer

                    # In the new protocol, we may not receive an initial message
                    # Just log the connection and emit the signal
                    logger.info("Connected to websocket")
                    self.connected_signal.emit(True)

                    # New format for subscription
                    subscribe_payload = {
                        "type": "SUBSCRIBE",
                        "data": {
                            "channel": self.event_channel_name
                        }
                    }
                    await self.websocket.send(json.dumps(subscribe_payload))
                    logger.info(f"Subscribing to channel: {self.event_channel_name}")

                    while self._running:
                        try:
                            # Trigger periodic cleanup
                            current_time = time.time()
                            if current_time - self.last_cleanup_time >= self.cleanup_interval:
                                self.cleanup_trigger_signal.emit()
                                self.last_cleanup_time = current_time

                            # Reduced timeout to detect connection issues faster
                            message = await asyncio.wait_for(self.websocket.recv(), timeout=20)
                            self.last_message_time = time.time()  # Update last message time
                            
                            parsed_message = json.loads(message)

                            # Handle the new message format
                            if isinstance(parsed_message, list):
                                for msg in parsed_message:
                                    msg_type = msg.get("type")

                                    # Handle PING message
                                    if msg_type == "PING":
                                        logger.debug("Received PING, sending PONG")
                                        pong_payload = [{"type": "PONG"}]
                                        await self.websocket.send(json.dumps(pong_payload))

                                    # Handle SUBSCRIBED confirmation
                                    elif msg_type == "SUBSCRIBED":
                                        channel = msg.get("channel")
                                        logger.info(f"Successfully subscribed to channel: {channel}")

                                    # Handle MESSAGE_PUBLISHED with seat updates
                                    elif msg_type == "MESSAGE_PUBLISHED":
                                        try:
                                            if "message" in msg and "body" in msg["message"]:
                                                seat_update = msg["message"]["body"]
                                                # Emit the seat update
                                                self.seat_data_updated.emit(seat_update)
                                                logger.debug(f"Processed seat update: {seat_update.get('objectLabelOrUuid', 'unknown')}")
                                        except (KeyError, TypeError) as e:
                                            logger.warning(f"Message decode/process error: {e}")
                                            logger.debug(f"Problematic message: {msg}")
                                            self.error_signal.emit(f"Data processing error: {e}")

                            # Handle error messages
                            elif isinstance(parsed_message, dict) and parsed_message.get("type") == "ERROR":
                                logger.error(f"Websocket error: {parsed_message.get('message')}")
                                self.error_signal.emit(f"Websocket error: {parsed_message.get('message')}")
                                self.connected_signal.emit(False)
                                break  # Break inner loop to trigger reconnection

                        except asyncio.TimeoutError:
                            # No message received within timeout, check connection with ping
                            logger.warning("No messages received for 20 seconds, checking connection...")
                            try:
                                pong_waiter = await self.websocket.ping()
                                await asyncio.wait_for(pong_waiter, timeout=5)
                                logger.info("Connection is still alive (ping successful)")
                                
                                # Even if ping is successful, check total silence duration
                                silence_duration = time.time() - self.last_message_time
                                if silence_duration > 40:  # Reconnect if no messages for 40+ seconds
                                    logger.warning(f"No messages for {silence_duration:.1f}s despite active connection, forcing reconnect")
                                    self.connected_signal.emit(False)
                                    break  # Break inner loop to trigger reconnection
                                    
                            except Exception as e:
                                logger.warning(f"Ping failed: {str(e)}, connection appears to be down")
                                self.connected_signal.emit(False)
                                break  # Break inner loop to trigger reconnection

                        except websockets.ConnectionClosed as e:
                            logger.warning(f"WebSocket connection closed: {e}")
                            self.connected_signal.emit(False)
                            break  # Break inner loop to trigger reconnection

                        except Exception as e:
                            logger.error(f"An error occurred: {e}")
                            self.error_signal.emit(str(e))
                            self.connected_signal.emit(False)
                            break  # Break inner loop to trigger reconnection

            except Exception as e:
                logger.error(f"Error connecting to WebSocket: {e}")
                self.error_signal.emit(f"WebSocket connection error: {e}")
                self.connected_signal.emit(False)

            # Don't retry if we're shutting down
            if not self._running:
                logger.info("WebSocket manager is shutting down, not attempting to reconnect")
                break

            # Implement exponential backoff for retries
            retry_count += 1
            if retry_count > max_retries:
                logger.error(f"Failed to reconnect after {max_retries} attempts. Signaling for reload.")
                # Signal complete disconnect to trigger reload in main window
                self.connected_signal.emit(False)
                
                # Instead of giving up completely, wait longer and try once more
                await asyncio.sleep(60)  # Wait a full minute
                retry_count = 0  # Reset retry count to try again
                continue

            delay = min(base_delay * (2 ** (retry_count - 1)), 20)  # Cap at 20 seconds
            logger.info(f"Will retry connection in {delay} seconds (attempt {retry_count}/{max_retries})...")
            await asyncio.sleep(delay)  # Wait before retry


    def stop(self):
        """Stops the WebSocket connection and the thread."""
        self._running = False
        
        # Stop the timers
        if self.heartbeat_timer.isActive():
            self.heartbeat_timer.stop()
            
        if self.reconnect_timer.isActive():
            self.reconnect_timer.stop()
            
        if self.websocket:
            # Use run_coroutine_threadsafe to schedule the coroutine in the correct loop.
            asyncio.run_coroutine_threadsafe(self.websocket.close(), self.loop)

        if self.thread and self.thread.isRunning():
            # Use call_soon_threadsafe to schedule loop.stop() in the correct loop.
            self.loop.call_soon_threadsafe(self.loop.stop)
            self.thread.quit()  # Signal the thread to exit its event loop
            self.thread.wait()  # Wait for the thread to finish
            self.thread = None # remove the thread