# account_token_manager.py - Manages account tokens for authorization

import requests
import logging
import json
import time
import threading
import random
from collections import deque

logger = logging.getLogger('webook_pro')

class AccountTokenManager:
    """
    Manages a pool of account tokens for use with the Webook API.
    Features rotation, validation, and automatic refresh of tokens.
    """
    
    def __init__(self, server_url="http://176.9.125.118:5000", initial_tokens=5):
        """
        Initialize the account token manager.
        
        Args:
            server_url: URL of the token server
            initial_tokens: Number of tokens to get on initialization
        """
        self.server_url = server_url
        self.tokens = deque()  # token info objects with access_token and metadata
        self.active_tokens = {}  # token -> last_used_timestamp
        self.failed_tokens = {}  # token -> {failure_count, last_failure}
        
        # Threading protection
        self.token_lock = threading.RLock()
        
        # Token refresh settings
        self.refresh_interval = 600  # 10 minutes
        self.last_refresh = 0
        self.token_ttl = 3600  # 1 hour
        
        # Initial tokens
        self._fetch_tokens(initial_tokens)
        
        # Start background refresh thread
        self._start_refresh_thread()
    
    def _fetch_tokens(self, count=5):
        """
        Fetch tokens from the token server.
        
        Args:
            count: Number of tokens to fetch
            
        Returns:
            Number of tokens successfully fetched
        """
        try:
            response = requests.get(f"{self.server_url}/api/tokens?count={count}")
            if response.status_code != 200:
                logger.error(f"Error fetching account tokens: {response.status_code} - {response.text}")
                return 0
            
            data = response.json()
            if data.get("status") != "success":
                logger.error(f"Error in token response: {data}")
                return 0
                
            # Process tokens
            with self.token_lock:
                token_count = 0
                for token_info in data.get("tokens", []):
                    if "access_token" in token_info:
                        # Store the whole token info object
                        self.tokens.append(token_info)
                        token_count += 1
                        
                # Update refresh time
                self.last_refresh = time.time()
            
            logger.info(f"Fetched {token_count} account tokens")
            return token_count
            
        except Exception as e:
            logger.error(f"Error fetching tokens: {str(e)}")
            return 0
    
    def get_token(self):
        """
        Get an account token for use with the API.
        
        Returns:
            Access token string or None if no tokens available
        """
        with self.token_lock:
            current_time = time.time()
            
            # Check if we need to refresh tokens
            if (current_time - self.last_refresh > self.refresh_interval) or not self.tokens:
                # Don't hold the lock during potentially slow network operation
                self.token_lock.release()
                self._fetch_tokens()
                self.token_lock.acquire()
            
            # If still no tokens, return None
            if not self.tokens:
                return None
            
            # Pop a token from the queue
            token_info = self.tokens.popleft()
            access_token = token_info.get("access_token")
            
            # Check if token is in failed list
            if access_token in self.failed_tokens:
                # If enough time has passed, give it another try
                failure_info = self.failed_tokens[access_token]
                if current_time - failure_info["last_failure"] > 300:  # 5 minutes cooldown
                    del self.failed_tokens[access_token]
                else:
                    # Try another token
                    self.tokens.append(token_info)  # Put it back at the end
                    return self.get_token()  # Recursively try again
            
            # Mark token as active
            self.active_tokens[access_token] = current_time
            
            # Put the token back at the end of the queue for future use
            self.tokens.append(token_info)
            
            return access_token
    
    def report_invalid_token(self, token):
        """
        Report an invalid token to avoid using it again.
        
        Args:
            token: The invalid token
            
        Returns:
            True if reported to server, False otherwise
        """
        with self.token_lock:
            # Find the token info object for this token
            email = None
            for token_info in self.tokens:
                if token_info.get("access_token") == token:
                    email = token_info.get("email")
                    break
            
            # Update local failed tokens tracking
            if token in self.failed_tokens:
                self.failed_tokens[token]["failure_count"] += 1
                self.failed_tokens[token]["last_failure"] = time.time()
            else:
                self.failed_tokens[token] = {
                    "failure_count": 1,
                    "last_failure": time.time()
                }
            
            # Remove the token from active tracking
            if token in self.active_tokens:
                del self.active_tokens[token]
        
        # Report to server if we have the email
        if email:
            try:
                response = requests.delete(f"{self.server_url}/api/account/{email}")
                if response.status_code != 200:
                    logger.error(f"Error reporting invalid token: {response.status_code} - {response.text}")
                    return False
                
                logger.info(f"Successfully reported invalid token for: {email}")
                return True
                
            except Exception as e:
                logger.error(f"Error reporting invalid token: {str(e)}")
                return False
        
        return False
    
    def _start_refresh_thread(self):
        """Start background thread to periodically refresh tokens"""
        def refresh_loop():
            while True:
                try:
                    # Sleep first to avoid immediate refresh
                    time.sleep(self.refresh_interval / 2)
                    
                    # Check if refresh is needed
                    with self.token_lock:
                        current_time = time.time()
                        if (current_time - self.last_refresh > self.refresh_interval) or len(self.tokens) < 3:
                            need_refresh = True
                        else:
                            need_refresh = False
                    
                    # Refresh if needed
                    if need_refresh:
                        self._fetch_tokens()
                        
                except Exception as e:
                    logger.error(f"Error in token refresh thread: {str(e)}")
                    # Sleep on error to avoid tight loop
                    time.sleep(10)
        
        # Start thread
        thread = threading.Thread(
            target=refresh_loop,
            daemon=True,
            name="AccountTokenRefresh"
        )
        thread.start()
        return thread
    
    def get_status(self):
        """
        Get the current status of token management.
        
        Returns:
            Dictionary of status information
        """
        with self.token_lock:
            return {
                "available_tokens": len(self.tokens),
                "active_tokens": len(self.active_tokens),
                "failed_tokens": len(self.failed_tokens),
                "time_since_refresh": time.time() - self.last_refresh
            }

# Global instance for easy access
_global_account_manager = None
_manager_lock = threading.Lock()

def get_account_token_manager():
    """Get or create the global account token manager"""
    global _global_account_manager
    
    with _manager_lock:
        if _global_account_manager is None:
            _global_account_manager = AccountTokenManager()
        
        return _global_account_manager

def get_account_token():
    """Convenience function to get an account token"""
    manager = get_account_token_manager()
    return manager.get_token()

def report_invalid_token(token):
    """Convenience function to report an invalid token"""
    manager = get_account_token_manager()
    return manager.report_invalid_token(token)
