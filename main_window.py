# main_window.py - Cleaned up and optimized version
import re
import time
import logging
from collections import defaultdict
import queue
from threading import Lock, Thread
import traceback
from PyQt5.QtWidgets import (
    QMainWindow, QApplication, QMessageBox, QTabWidget, QLineEdit,
    QTextEdit, QPushButton, QVBoxLayout, QW<PERSON>t, QHBoxLayout, QFormLayout,
    QLabel, QComboBox, QCheckBox
)
from PyQt5.QtCore import Qt, QEvent, QThread, QCoreApplication, QTimer, QThreadPool
import random
import threading
from PyQt5.QtCore import QTime, QTimer
import json
# Your existing helpers
from helper import (
    get_webook_event_info,
    get_object_statuses,
    group_tickets_by_type_and_status,
    release_seat,
    get_event_seatsio_info
)
from elitesoftworks import print_logo, get_machine_hwid
from proxy_config_dialog import ProxyConfigDialog
from token_retrieval import cache_event_id, get_cached_event_id

# Manager, threads, tabs, workers
from chart_token_manager import get_chart_token
from manager.proxy_manager import get_global_proxy_manager
from fast_hold import FastHoldManager
from threads.time_left_updater_thread import TimeLeftUpdaterThread
from threads.event_websocket_thread import WebSocketManager
from token_management_system import TokenManagementSystem
from tabs.held_seats_tab import HeldSeatsTab
from tabs.ticket_type_selection_tab import TicketTypeSelectionTab
from performance_testing import PerformanceStats, PerformanceMonitorDialog
from script_info import SCRIPT_NAME, VERSION, MAX_SEATS_PER_TOKEN

logger = logging.getLogger("webook_pro")

def perform_hwid_check(username: str):
    """
    Stub function. Replace with real logic if needed.
    """
    return True


class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle(f"{SCRIPT_NAME} v{VERSION}")
        self.resize(1000, 600)

        # Initialize data structures
        self.tickets_info = defaultdict(lambda: defaultdict(dict))  # type -> status -> {seat_id: seat_data}
        self.seat_id_map = {}  # seat_id: {'type': str, 'status': str}

        # Setup UI components
        self.tab_widget = QTabWidget()
        self.setCentralWidget(self.tab_widget)

        self.booking_tab = QWidget()
        self.create_booking_tab_ui()
        self.tab_widget.addTab(self.booking_tab, "Booking")

        self.held_seats_tab = HeldSeatsTab(self)
        self.held_seats_tab.setEnabled(False)
        self.tab_widget.addTab(self.held_seats_tab, "Held Seats")

        self.ticket_type_tab = TicketTypeSelectionTab(self)
        self.ticket_type_tab.setEnabled(False)
        self.tab_widget.addTab(self.ticket_type_tab, "Ticket Type Selection")

        # Initialize proxy config
        self.proxy_config = {
            "enabled": False,
            "domain": "p.webshare.io",
            "port": "80",
            "username": "taplmftg-rotate",
            "password": "P@SSWORD"
        }

        # Initialize chart token refresher
        self.initialize_chart_token()

        # Core system components
        self.token_system = None
        self.fast_hold_manager = None
        self.websocket_manager = None

        # Threading and concurrency management
        self.booking_lock = Lock()
        self.data_lock = Lock()
        self.thread_pool = QThreadPool()
        self.thread_pool.setMaxThreadCount(4)

        # Initialize updater thread
        self.time_left_updater = TimeLeftUpdaterThread(interval=1)
        self.time_left_updater.setObjectName("TimeLeftUpdaterThread")
        self.time_left_updater.update_signal.connect(self.held_seats_tab.auto_refresh_held_seats)
        self.time_left_updater.refresh_signal.connect(self.refresh_all_token_times)
        self.time_left_updater.start()

        # Auto-hold tracking
        self.pending_seats = 0
        self.auto_held_seats = {}

        # Start cleanup timer
        self.cleanup_timer = QTimer()
        self.cleanup_timer.timeout.connect(self.cleanup_zombie_seats)
        self.cleanup_timer.start(10000)  # Run cleanup every 10 seconds

    #------------------------------------------------------------------
    # Core initialization and cleanup methods
    #------------------------------------------------------------------

    def initialize_chart_token(self):
        """Initialize chart token when the application starts"""
        # Force an initial fetch of the chart token
        chart_token = get_chart_token(force_refresh=True, proxy=self.get_proxy_string())
        if chart_token:
            self.log(f"🔑 Chart token initialized: {chart_token[:8]}...")
        else:
            self.log("⚠️ Failed to initialize chart token")

    def reset_auto_held_seats(self):
        """
        Reset the auto_held_seats dictionary and update the window title.
        This allows the system to try holding seats that previously failed.
        """
        with self.data_lock:
            self.auto_held_seats = {}
            self.pending_seats = 0

        # Reset window title
        current_title = self.windowTitle()
        if '(' in current_title:
            base_title = current_title.split('(')[0].strip()
            held_count = 0
            if self.token_system:
                held_count = len(self.token_system.get_all_seats())

            if held_count > 0:
                self.setWindowTitle(f"{base_title} (Auto-held: {held_count})")
            else:
                self.setWindowTitle(base_title)

        self.log("🔄 Auto-hold tracking reset - previously failed seats will be eligible for auto-hold")

        # Update UI
        self.held_seats_tab.auto_refresh_held_seats(force_rebuild=True)

    def _initialize_token_management_system(self):
        """Initialize the centralized token management system"""
        # Create the token management system
        self.token_system = TokenManagementSystem(
            event_key=self.webook_data['data']['seats_io']['event_key'],
            chart_key=self.webook_data['data']['seats_io']['chart_key'],
            channel_keys=self.webook_data['data']['channel_keys'],
            team_id=self.team_combo.currentData(),
            proxies=[self.get_proxy_string()] if self.get_proxy_string() else []
        )

        self.token_system.log_signal.connect(self.log)
        self.token_system.seat_held_signal.connect(self.on_seat_held)
        self.token_system.seat_released_signal.connect(self.on_seat_released)
        self.token_system.seat_transferred_signal.connect(self.on_seat_transferred)
        self.token_system.seat_expired_signal.connect(self.on_seats_expired)
        self.token_system.ui_update_signal.connect(self.held_seats_tab.auto_refresh_held_seats)
        self.token_system.token_renewed_signal.connect(self.on_token_renewed)

        self.log("TokenManagementSystem initialized")


    def initialize_proxy_system(self):
        """Initialize the proxy system with current settings"""
        # Initialize the global proxy manager with current settings
        proxy_manager = get_global_proxy_manager(self.proxy_config)

        # Log proxy configuration
        if self.proxy_config.get("enabled", False):
            if self.proxy_config.get("mode", "single") == "single":
                domain = self.proxy_config.get("domain", "")
                port = self.proxy_config.get("port", "")
                user = self.proxy_config.get("username", "")
                rotating = self.proxy_config.get("use_rotating", True)

                if rotating:
                    self.log(f"Proxy enabled: {domain}:{port} with rotation (User: {user})")
                else:
                    self.log(f"Proxy enabled: {domain}:{port} without rotation (User: {user})")
            else:
                proxy_count = len(self.proxy_config.get("proxy_list", []))
                rotation = self.proxy_config.get("local_rotation", True)
                rotation_count = self.proxy_config.get("rotation_count", 50)

                if rotation:
                    self.log(f"Local proxy rotation enabled with {proxy_count} proxies. Rotating every {rotation_count} requests.")
                else:
                    self.log(f"Local proxy list enabled with {proxy_count} proxies. No automatic rotation.")


    def _start_fast_holding_system(self):
        """Initialize the FastHoldManager for rapid seat holding"""
        # Get event_id if available
        event_id = None
        if hasattr(self, 'webook_data') and self.webook_data:
            event_id = self.webook_data.get("data", {}).get("_id")

        self.fast_hold_manager = FastHoldManager(
            event_key=self.webook_data['data']['seats_io']['event_key'],
            chart_key=self.webook_data['data']['seats_io']['chart_key'],
            channel_keys=self.webook_data['data']['channel_keys'],
            team_id=self.team_combo.currentData(),
            proxy=self.get_proxy_string(),
            event_id=event_id  # Pass event_id to FastHoldManager
        )

        # Store reference to main_window in FastHoldManager for later access
        self.fast_hold_manager.main_window = self

        # Connect the seat held signal to our handler
        self.fast_hold_manager.seat_held_signal.connect(self.on_fast_hold_seat_held)
        self.fast_hold_manager.log_signal.connect(self.log)
        self.fast_hold_manager.performance_signal.connect(self.on_performance_signal)

        # Register FastHoldManager with the token system
        if hasattr(self, 'token_system') and self.token_system:
            self.token_system.register_fast_hold_manager(self.fast_hold_manager)

        self._start_token_renewal_timer()



    def _start_token_renewal_timer(self):
        """Start a timer to periodically check if tokens need renewal"""
        self.renewal_timer = QTimer()
        self.renewal_timer.timeout.connect(self._check_and_renew_token)
        self.renewal_timer.start(60 * 1000) # Check every minute

    def _check_and_renew_token(self):
        """Check if the FastHoldManager token needs renewal"""
        if self.fast_hold_manager:
            self.fast_hold_manager.renew_token_if_needed()

    def _initialize_websocket(self, data):
        """Initialize WebSocket connection for real-time seat status updates"""
        if self.websocket_manager and self.websocket_manager.thread.isRunning():
            self.websocket_manager.stop()

        # Handle season_event_key correctly
        season_event_key = data['event_info'].get('season_info', {})
        if season_event_key:
            season_event_key = season_event_key.get('topLevelSeasonKey')
        event_key = season_event_key or data["event_key"]

        self.websocket_manager = WebSocketManager(
            event_key,
            data["chart_key"],
            parent=None
        )
        self.websocket_manager.seat_data_updated.connect(self.process_single_seat_update)
        self.websocket_manager.error_signal.connect(self.on_refresh_error)
        self.websocket_manager.connected_signal.connect(self.on_websocket_connected)
        self.websocket_manager.start()

    def _update_initial_ui(self):
        """Initial UI update after loading event data"""
        self.ticket_type_tab.update_tickets_info(self.tickets_info)
        self.held_seats_tab.auto_refresh_held_seats()

    #------------------------------------------------------------------
    # Token and seat event handlers
    #------------------------------------------------------------------

    def on_fast_hold_seat_held(self, seat_number, token):
        """
        Optimized handler for seats held by FastHoldManager.
        Focuses on speed by eliminating unnecessary operations.
        """
        try:
            # Get timing data only if we need it
            elapsed_ms = 0
            with self.data_lock:
                if seat_number in self.auto_held_seats:
                    start_time = self.auto_held_seats[seat_number]
                    if isinstance(start_time, float):
                        elapsed_ms = (time.time() - start_time) * 1000
                        
                # Reduce pending count immediately
                self.pending_seats = max(0, self.pending_seats - 1)
                
                # Remove from auto_held_seats to allow holding other seats
                self.auto_held_seats.pop(seat_number, None)

            # Don't log success to avoid overhead on critical path
            
            # Check if max tickets reached - use direct token_system check
            max_tickets = 0
            try:
                max_tickets_text = self.total_tickets_edit.text()
                if max_tickets_text:
                    max_tickets = int(max_tickets_text)
            except ValueError:
                pass

            # Only check max tickets if there's a limit
            if max_tickets > 0:
                # Get seat count from token system directly
                if self.token_system:
                    held_seats = len(self.token_system.get_all_seats())
                    if held_seats > max_tickets:
                        # Release excess seat in background
                        self._release_excess_seat(seat_number, token)
                        return

            # Register with TokenManagementSystem - most important part
            if self.token_system:
                self.token_system.register_held_seat(seat_number, token)
                
                # Trigger UI update asynchronously
                QTimer.singleShot(50, lambda: self.held_seats_tab.auto_refresh_held_seats(force_rebuild=True))
                QTimer.singleShot(75, self._update_window_title)

        except Exception as e:
            # Silent exception handling on critical path
            pass


    def initialize_channel_key_refresh_timer(self):
        """Initialize timer for checking channel key updates"""
        self.channel_key_timer = QTimer()
        self.channel_key_timer.timeout.connect(self.check_channel_key_updates)
        
        # Calculate time until next half-hour mark
        current_time = QTime.currentTime()
        current_minute = current_time.minute()
        current_second = current_time.second()
        
        # Calculate milliseconds until next check time (start of hour or half-hour)
        if current_minute < 30:
            # Time until 30-minute mark
            minutes_to_wait = 30 - current_minute
        else:
            # Time until next hour
            minutes_to_wait = 60 - current_minute
        
        # Subtract seconds already elapsed in the current minute
        seconds_to_wait = minutes_to_wait * 60 - current_second
        msecs_to_wait = seconds_to_wait * 1000
        
        # Start one-shot timer for first alignment to hour/half-hour
        QTimer.singleShot(msecs_to_wait, self.start_periodic_channel_key_check)
        
        self.log("🔄 Channel key refresh timer initialized. First check in " + 
                f"{minutes_to_wait} minutes and {current_second} seconds")

    def start_periodic_channel_key_check(self):
        """Start the periodic channel key check timer"""
        # Now we're aligned to hour/half-hour, start the regular 30-minute timer
        self.channel_key_timer.start(30 * 60 * 1000)  # 30 minutes in milliseconds
        
        # Also perform the first check immediately
        self.check_channel_key_updates()

    def check_channel_key_updates(self):
        """Check for updates to channel keys and reload if necessary"""
        if not hasattr(self, 'webook_data') or not self.webook_data:
            self.log("⚠️ No event loaded, skipping channel key check")
            return
        
        try:
            # Get current time for logging
            current_time = QTime.currentTime()
            time_str = current_time.toString("HH:mm:ss")
            self.log(f"🔍 {time_str} - Scheduled channel key check running...")
            
            # Use the enhanced reload_event_data method
            event_key = self.webook_data['data']['seats_io']['event_key']
            success = self.reload_event_data(event_key)
            
            if success:
                self.log(f"✓ {time_str} - Channel key check completed successfully")
                
                # Update the window title to reflect any changes in held seats
                self._update_window_title()
                
                # Force refresh of held seats tab to ensure accurate data
                if hasattr(self, 'held_seats_tab'):
                    self.held_seats_tab.auto_refresh_held_seats(force_rebuild=True)
                
                # Update ticket type selection tab to ensure accurate data
                if hasattr(self, 'ticket_type_tab'):
                    self.ticket_type_tab.update_tickets_info(self.tickets_info)
            else:
                self.log(f"⚠️ {time_str} - Channel key check failed, will retry at next scheduled time")
                
        except Exception as e:
            self.log(f"❌ Error in scheduled channel key check: {str(e)}")
            logger.error(f"Error in scheduled channel key check: {str(e)}", exc_info=True)



    def on_seats_removed(self, expired_seats):
        """Handle seats that have been removed due to expiration"""
        if not expired_seats:
            return

        # Log the removals
        self.log(f"⚠️ {len(expired_seats)} seats removed due to token expiration")

        # Update the seat data structures
        with self.data_lock:
            for seat_id in expired_seats:
                # Remove from auto_held_seats if it's there
                self.auto_held_seats.pop(seat_id, None)

                # Update seat status in tickets_info if we know about it
                if seat_id in self.seat_id_map:
                    seat_type = self.seat_id_map[seat_id]['type']
                    old_status = self.seat_id_map[seat_id]['status']

                    # Remove from old status dictionary
                    if seat_type in self.tickets_info and old_status in self.tickets_info[seat_type]:
                        self.tickets_info[seat_type][old_status].pop(seat_id, None)

                    # Update seat status to unknown (it might become free again)
                    self.seat_id_map[seat_id]['status'] = 'unknown'

        # Update the UI
        self.held_seats_tab.auto_refresh_held_seats(force_rebuild=True)

    def on_seat_held(self, seat_id, token_id):
        """Handle new seat successfully held by TokenManagementSystem"""
        with self.data_lock:
            self.auto_held_seats.pop(seat_id, None)
            self.pending_seats -= 1

        # Update UI
        self.held_seats_tab.auto_refresh_held_seats(force_rebuild=True)

        # Update window title
        self._update_window_title()

    def on_seat_released(self, seat_id):
        """Handle seat released by TokenManagementSystem"""
        # Update UI
        self.held_seats_tab.auto_refresh_held_seats(force_rebuild=True)

        # Update window title
        self._update_window_title()

    def on_seat_transferred(self, seat_id, token_id, success):
        """Handle seat transfer result from TokenManagementSystem"""
        if success:
            self.log(f"Seat {seat_id} transferred to token {token_id}")
            with self.data_lock:
                if seat_id in self.seat_id_map:
                    seat_type = self.seat_id_map[seat_id]['type']
                    old_status = self.seat_id_map[seat_id]['status']
                    if seat_type in self.tickets_info and old_status in self.tickets_info[seat_type]:
                        self.tickets_info[seat_type][old_status].pop(seat_id, None)
                    self.seat_id_map[seat_id]['status'] = 'transferred'
        else:
            self.log(f"Failed to transfer seat {seat_id} to token {token_id}")
        self.held_seats_tab.auto_refresh_held_seats(force_rebuild=True)
        self._update_window_title()

    def on_seats_expired(self, seat_ids):
        """Handle seats expired in TokenManagementSystem"""
        self.log(f"⚠️ {len(seat_ids)} seats expired")

        # Update seat status in tickets_info
        with self.data_lock:
            for seat_id in seat_ids:
                # Update seat status in tickets_info if we know about it
                if seat_id in self.seat_id_map:
                    seat_type = self.seat_id_map[seat_id]['type']
                    old_status = self.seat_id_map[seat_id]['status']

                    # Remove from old status dictionary
                    if seat_type in self.tickets_info and old_status in self.tickets_info[seat_type]:
                        self.tickets_info[seat_type][old_status].pop(seat_id, None)

                    # Update seat status to unknown
                    self.seat_id_map[seat_id]['status'] = 'unknown'

        # Update UI
        self.held_seats_tab.auto_refresh_held_seats(force_rebuild=True)

        # Update window title
        self._update_window_title()

    def _update_window_title(self):
        """Update window title with current held seat count"""
        try:
            # Only use token_system for seat count
            held_seats_count = 0
            if self.token_system:
                held_seats_count = len(self.token_system.get_all_seats())
            else:
                return  # Don't update if no token system

            current_title = self.windowTitle()
            if '(' in current_title and 'Auto-held:' in current_title:
                base_title = current_title.split('(')[0].strip()
                self.setWindowTitle(f"{base_title} (Auto-held: {held_seats_count})")
        except Exception as e:
            logger.error(f"Error updating window title: {str(e)}")


    def _release_excess_seat(self, seat_number, token):
        """Optimized excess seat release with minimal overhead"""
        # Use a background thread to not block the UI
        def release_background():
            try:
                if self.token_system:
                    self.token_system.release_seat(seat_number)
            except:
                pass

        # Spawn thread without logging
        threading.Thread(target=release_background, daemon=True).start()
    #------------------------------------------------------------------
    # Seat data processing and auto-hold logic
    #------------------------------------------------------------------

    def cleanup_zombie_seats(self):
        """
        Automatically clean up "zombie" seats - those that are in auto_held_seats
        but aren't actually being processed or held successfully.
        Updated to use only token_system.
        """
        with self.data_lock:
            # Nothing to clean if no auto-held seats
            if not self.auto_held_seats:
                return

            # Seats that are actually held by token system
            actually_held_seats = set()
            if self.token_system:
                actually_held_seats = set(self.token_system.get_all_seats().keys())
            else:
                # No token system
                return

            # Find zombie seats - in auto_held_seats but not in token system
            zombie_seats = []

            for seat_id in list(self.auto_held_seats.keys()):
                if seat_id not in actually_held_seats:
                    zombie_seats.append(seat_id)
                    self.auto_held_seats.pop(seat_id, None)

            # Adjust pending_seats if needed to match reality
            total_held = len(actually_held_seats)
            total_auto_held = len(self.auto_held_seats)

            # If pending_seats is out of sync, reset it based on auto_held_seats
            if self.pending_seats != total_auto_held:
                self.log(f"⚠️ Auto-hold count mismatch: pending={self.pending_seats}, tracked={total_auto_held}")
                self.pending_seats = total_auto_held

            # If we found zombie seats, log and update
            if zombie_seats:
                self.log(f"🧟 Cleaned up {len(zombie_seats)} zombie seats from auto-hold tracking")

                # Update window title to show correct count
                current_title = self.windowTitle()
                if '(' in current_title and 'Auto-held:' in current_title:
                    base_title = current_title.split('(')[0].strip()
                    self.setWindowTitle(f"{base_title} (Auto-held: {total_held})")

                # Update ticket type UI to reflect freed seats
                for seat_id in zombie_seats:
                    if seat_id in self.seat_id_map:
                        seat_type = self.seat_id_map[seat_id]['type']
                        self.ticket_type_tab.update_type_row(seat_type)

    def process_single_seat_update(self, seat_data):
        """
        Ultra-optimized seat update processing with critical path optimizations.
        Reduces auto-hold decision time to absolute minimum with early exits.
        """
        # Quick check for required data (FAST PATH)
        if not seat_data or 'objectLabelOrUuid' not in seat_data:
            return

        # Extract seat_id for use in all checks
        seat_id = seat_data['objectLabelOrUuid']

        # ===== FAST PATH - EARLY EXITS =====
        
        # 1. Skip if status isn't free (first and fastest check)
        status = seat_data.get('status', '')
        if status != 'free':
            return
        
        # 2. Skip if already being processed (nearly as fast)
        if seat_id in self.auto_held_seats:
            return
        
        # 3. Skip if auto-hold is disabled (very fast)
        if not self.ticket_type_tab.auto_hold_checkbox.isChecked():
            return
        
        # 4. Skip if being transferred (check this early)
        if seat_id in self.token_system.transfer_in_progress_seats:
            return

        # 5. Fast check for max tickets
        # Get these values just once to avoid repeated property access
        max_tickets = 0
        try:
            max_tickets_text = self.total_tickets_edit.text()
            if max_tickets_text:
                max_tickets = int(max_tickets_text)
        except ValueError:
            pass

        if max_tickets > 0:
            # Fast seat count without locks
            current_held_count = len(self.token_system.get_all_seats())
            pending_holds = getattr(self, 'pending_seats', 0)
            
            if current_held_count + pending_holds >= max_tickets:
                return
        
        # ===== TICKET TYPE DETERMINATION (Optimized) =====
        
        # Fast type extraction - avoid string operations when possible
        seat_parts = seat_id.split('-', 1)  # Split only at first dash
        seat_type = seat_parts[0].strip() if seat_parts else seat_id
        
        # Fast path for "hold all" mode
        hold_all = getattr(self.ticket_type_tab, 'hold_all_enabled', False)
        
        # Only check selected types if not in "hold all" mode
        if not hold_all:
            selected_types = self.ticket_type_tab.get_selected_types()
            if '*' not in selected_types and seat_type not in selected_types:
                return
        
        # ===== EXECUTION PHASE (Critical path) =====
        
        # At this point, we've decided to hold the seat
        # Use an atomic update to mark the seat as pending
        with self.data_lock:
            if seat_id in self.auto_held_seats:
                return
            
            # Mark as pending with timestamp for performance tracking
            self.auto_held_seats[seat_id] = time.time()
            self.pending_seats += 1
        
        # Launch the actual hold without holding locks
        self._launch_ultra_fast_hold(seat_id, seat_type)


    def _launch_ultra_fast_hold(self, seat_id, seat_type):
        """Ultra-optimized seat holding function with minimal overhead"""
        # Skip any logging during critical path to avoid overhead
        # Just call hold_seat directly
        if self.fast_hold_manager:
            self.fast_hold_manager.hold_seat(seat_id)
            
            # Schedule window title update asynchronously
            QTimer.singleShot(50, self._update_window_title)
            
            return True
        
        return False


    def _process_non_free_seat(self, seat_data):
        """Process non-free seats with lower priority"""
        # Schedule this to run after critical operations
        QTimer.singleShot(50, lambda: self._update_seat_data(seat_data))

    def _process_non_auto_seat(self, seat_data):
        """Process free seats that we're not auto-holding"""
        # Schedule this to run after critical operations
        QTimer.singleShot(10, lambda: self._update_seat_data(seat_data))

    def _update_seat_data(self, seat_data):
        """Update internal seat data structures (non-critical path)"""
        try:
            with self.data_lock:
                seat_id = seat_data['objectLabelOrUuid']
                new_status = seat_data.get('status', 'free').lower()

                # Extract seat type
                seat_parts = seat_id.split('-')
                seat_type = seat_parts[0].strip() if seat_parts else seat_id

                # Get existing seat info
                current_info = self.seat_id_map.get(seat_id)

                if current_info:
                    # Seat already tracked - update status
                    previous_status = current_info['status']
                    if new_status != previous_status:
                        # Remove from old status dictionary
                        if seat_type in self.tickets_info and previous_status in self.tickets_info[seat_type]:
                            self.tickets_info[seat_type][previous_status].pop(seat_id, None)

                        # Add to new status dictionary
                        if seat_type not in self.tickets_info:
                            self.tickets_info[seat_type] = {}
                        if new_status not in self.tickets_info[seat_type]:
                            self.tickets_info[seat_type][new_status] = {}
                        self.tickets_info[seat_type][new_status][seat_id] = seat_data
                        self.seat_id_map[seat_id]['status'] = new_status
                else:
                    # New seat - add to tracking
                    self.seat_id_map[seat_id] = {
                        'type': seat_type,
                        'status': new_status
                    }

                    # Add to status dictionary
                    if seat_type not in self.tickets_info:
                        self.tickets_info[seat_type] = {}
                    if new_status not in self.tickets_info[seat_type]:
                        self.tickets_info[seat_type][new_status] = {}
                    self.tickets_info[seat_type][new_status][seat_id] = seat_data

            # Update UI for this seat type
            self.ticket_type_tab.update_type_row(seat_type)
        except Exception as e:
            logger.error(f"Error updating seat data: {str(e)}")

    #------------------------------------------------------------------
    # Token refresh and management
    #------------------------------------------------------------------

    def refresh_all_token_times(self):
        """
        Refresh the time left for all tokens by calling activate_hold_token
        Now only uses the token system
        """
        logger.debug("refresh_all_token_times called")

        # Use token system, which handles its own refresh
        if self.token_system:
            self.held_seats_tab.auto_refresh_held_seats(True)
            return

        # No token system or tokens - just log a message
        # self.log("No token system initialized. Cannot refresh token times.")



    def on_token_refresh_finished(self, count):
        """Called when all token refreshing is finished"""
        if count > 0:
            self.log(f"Refreshed {count} token times from server")

        # Update the UI times - but only the time columns, not full rebuild
        if hasattr(self, 'held_seats_tab'):
            self.held_seats_tab.update_time_displays_only()

    #------------------------------------------------------------------
    # UI Setup and Event Handlers
    #------------------------------------------------------------------

    def create_booking_tab_ui(self):
        """Create the main booking tab UI"""
        layout = QVBoxLayout()

        self.auth_name_edit = QLineEdit()
        self.auth_name_edit.setPlaceholderText("Enter your username...")

        self.hwid_check_button = QPushButton("Check username")

        self.event_key_edit = QLineEdit()
        self.event_key_edit.setPlaceholderText("Enter event key...")

        self.total_tickets_edit = QLineEdit()
        self.total_tickets_edit.setPlaceholderText("Enter total seats to book...")

        self.team_combo = QComboBox()

        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)

        self.load_button = QPushButton("Load Event Info")
        self.book_button = QPushButton("Book Seats (Concurrent)")
        self.release_button = QPushButton("Release All Seats")
        self.about_button = QPushButton("About")
        self.configure_proxy_button = QPushButton("Configure Proxy")
        self.performance_button = QPushButton("Performance Monitor")

        self.performance_button.clicked.connect(self.show_performance_monitor)

        form_layout = QFormLayout()
        form_layout.addRow("Auth Name:", self.auth_name_edit)
        form_layout.addRow("", self.hwid_check_button)
        form_layout.addRow("Event Key:", self.event_key_edit)
        form_layout.addRow("Total Seats:", self.total_tickets_edit)
        form_layout.addRow("Choose Team:", self.team_combo)
        form_layout.addRow(self.configure_proxy_button)
        form_layout.addRow(self.performance_button)

        button_layout = QHBoxLayout()
        button_layout.addWidget(self.load_button)
        button_layout.addWidget(self.book_button)
        button_layout.addWidget(self.release_button)
        button_layout.addWidget(self.about_button)

        layout.addLayout(form_layout)
        layout.addLayout(button_layout)
        layout.addWidget(self.log_text)

        self.booking_tab.setLayout(layout)

        # Disable until HWID check
        self.set_fields_enabled(False)
        self.about_button.setEnabled(True)

        # Connect signals
        self.configure_proxy_button.clicked.connect(self.on_configure_proxy)
        self.hwid_check_button.clicked.connect(self.on_hwid_check)
        self.load_button.clicked.connect(self.on_load_event_info)
        self.book_button.clicked.connect(self.on_book_seats)
        self.release_button.clicked.connect(self.on_release_seats)
        self.about_button.clicked.connect(self.on_about_clicked)

    def set_fields_enabled(self, enabled: bool):
        """Enable or disable form fields"""
        self.event_key_edit.setEnabled(enabled)
        self.total_tickets_edit.setEnabled(enabled)
        self.team_combo.setEnabled(enabled)
        self.load_button.setEnabled(enabled)
        self.book_button.setEnabled(enabled)
        self.release_button.setEnabled(enabled)

    def on_configure_proxy(self):
        """Handle proxy configuration button click with enhanced proxy system"""
        dialog = ProxyConfigDialog(self)

        # Set current proxy configuration
        current_config = {
            "enabled": self.proxy_config.get("enabled", False),
            "domain": self.proxy_config.get("domain", "p.webshare.io"),
            "port": self.proxy_config.get("port", "80"),
            "username": self.proxy_config.get("username", "taplmftg-rotate"),
            "password": self.proxy_config.get("password", ""),
            "proxy_list": self.proxy_config.get("proxy_list", []),
            "use_rotating": self.proxy_config.get("use_rotating", True),
            "api_token": self.proxy_config.get("api_token", ""),
            "rotation_count": self.proxy_config.get("rotation_count", 50),
            "local_rotation": self.proxy_config.get("local_rotation", True),
            "mode": self.proxy_config.get("mode", "single")
        }

        if dialog.exec_() == dialog.Accepted:
            new_settings = dialog.get_proxy_settings()
            self.proxy_config = new_settings

            # Initialize the global proxy manager with new settings
            proxy_manager = get_global_proxy_manager(new_settings)
            self.initialize_proxy_system()
            # Log proxy configuration
            if new_settings["mode"] == "single":
                if new_settings["enabled"]:
                    if new_settings["use_rotating"]:
                        self.log(f"Proxy enabled: {new_settings['domain']}:{new_settings['port']} with rotation (User: {new_settings['username']})")
                    else:
                        self.log(f"Proxy enabled: {new_settings['domain']}:{new_settings['port']} without rotation (User: {new_settings['username']})")
                else:
                    self.log("Proxy disabled.")
            else:
                proxy_count = len(new_settings.get("proxy_list", []))
                if new_settings["local_rotation"]:
                    self.log(f"Local proxy rotation enabled with {proxy_count} proxies. Rotating every {new_settings['rotation_count']} requests.")
                else:
                    self.log(f"Local proxy list enabled with {proxy_count} proxies. No automatic rotation.")
        else:
            self.log("Proxy config canceled by user.")


    def record_proxy_performance(self, proxy_str, success, response_time_ms):
        """Record proxy performance metrics"""
        proxy_manager = get_global_proxy_manager()
        if proxy_manager and proxy_str:
            proxy_manager.report_result(proxy_str, success, response_time_ms)

    def on_hwid_check(self):
        """Handle hardware ID check button click"""
        username = self.auth_name_edit.text().strip()
        if not username:
            QMessageBox.warning(self, "Warning", "Please enter a valid username.")
            return

        if perform_hwid_check(username):
            QMessageBox.information(self, "Success", f"Welcome {username}!")
            self.set_fields_enabled(True)
            self.held_seats_tab.setEnabled(True)
            self.ticket_type_tab.setEnabled(True)
            self.log(f"Welcome, {username}!")
            self.setWindowTitle(f"{SCRIPT_NAME} v{VERSION} - {username}")
            logger.info(f"HWID check passed for user {username}.")
        else:
            QMessageBox.critical(self, "Error", "Username check failed.")
            logger.error(f"HWID check failed for user {username}.")
            self.set_fields_enabled(False)
            self.held_seats_tab.setEnabled(False)
            self.ticket_type_tab.setEnabled(False)

    def reload_event_data_and_reconnect(self):
        """Reload event data and reconnect WebSocket when connection is lost"""
        # Only reload if we have event data already
        if not hasattr(self, 'webook_data') or not self.webook_data:
            self.log("[WebSocket] No event data to reload")
            return False

        event_key = self.webook_data['data']['seats_io']['event_key']
        self.log(f"[WebSocket] Reloading event data for {event_key} after disconnection...")

        try:
            # Reload webook data
            self.webook_data = get_webook_event_info(event_key=event_key)
            if not self.webook_data.ok:
                self.log(f'[WebSocket] Failed to reload event data: {self.webook_data.reason}')
                return False

            self.webook_data = self.webook_data.json()
            data = self.webook_data["data"]["seats_io"]
            data['event_info'] = get_event_seatsio_info(data)

            # Reload seats data
            chart_key = data["chart_key"]
            seats = get_object_statuses(data["event_key"], chart_key)
            self.tickets_info = group_tickets_by_type_and_status(seats, free_only=False)

            # Reinitialize seat map
            for ttype, statuses in self.tickets_info.items():
                for status, seats in statuses.items():
                    for seat in seats:
                        seat_id = seat
                        self.seat_id_map[seat_id] = {
                            'type': ttype,
                            'status': status
                        }

            # Reinitialize WebSocket with fresh connection
            self._initialize_websocket(data)

            # Refresh UI
            self._update_initial_ui()

            self.log("[WebSocket] Event data reloaded and WebSocket reconnected successfully")
            return True

        except Exception as e:
            self.log(f"[WebSocket] Error reloading event data: {str(e)}")
            logger.exception("Error reloading event data", exc_info=True)
            return False

    # Modify the on_websocket_connected method in main_window.py
    def on_websocket_connected(self, connected):
        """Handle WebSocket connection status changes"""
        if connected:
            self.log("[WebSocket] Connected to event updates.")
        else:
            self.log("[WebSocket] Disconnected from event updates. Will attempt to reconnect...")
            # Schedule a reload attempt after a short delay
            QTimer.singleShot(5000, self.reload_event_data_and_reconnect)

    def show_performance_monitor(self):
        """Show the performance monitor dialog"""
        if not hasattr(self, 'performance_stats'):
            # Create stats tracker if it doesn't exist
            self.performance_stats = PerformanceStats()

            # Connect signals from fast hold manager
            if hasattr(self, 'fast_hold_manager') and self.fast_hold_manager:
                self.fast_hold_manager.performance_signal.connect(
                    lambda stats: self.performance_stats.record_network_time(stats.get('avg_ms', 0))
                )

        # Create and show the dialog
        dialog = PerformanceMonitorDialog(self.performance_stats, self)
        dialog.exec_()

    def on_about_clicked(self):
        """Handle about button click"""
        msg = QMessageBox(self)
        msg.setWindowTitle("About Webook Booking Pro")
        msg.setTextFormat(Qt.RichText)
        msg.setText(
            f"<h3>Webook Booking Pro version {VERSION}</h3>"
            "<p>Visit our website: "
            "<a href='https://elitesoftworks.com'>elitesoftworks.com</a></p>"
            "<p>Join our Telegram: "
            "<a href='https://t.me/NoodlesRush'>@NoodlesRush</a></p>"
        )
        msg.setStandardButtons(QMessageBox.Ok)
        msg.exec_()

    def log(self, message: str):
        """Writes to the GUI log and system logger"""
        self.log_text.append(message)
        logger.info(message)

    def on_load_event_info(self):
        """Handle load event info button click with support for event ID caching"""
        event_key = self.event_key_edit.text().strip()
        if not event_key:
            QMessageBox.warning(self, "Warning", "Please enter an event key.")
            return

        # Possibly extract event_key from a URL
        if '/' in event_key:
            found = re.search(r'events?/(.*?)(?:/|$)', event_key)
            if found:
                event_key = found.group(1)
        event_key = event_key.replace('--', '-')

        try:
            self.webook_data = get_webook_event_info(event_key=event_key)
            if not self.webook_data.is_success:
                logger.error(f'Webook refused to give us the data with the reason: {self.webook_data.reason}, please try again in a few seconds')
                return None
            else:
                self.webook_data = self.webook_data.json()

                # Cache the event ID in the token retrieval system
                # This is crucial for the new token retrieval mechanism
                cache_event_id(self.webook_data)

            data = self.webook_data["data"]["seats_io"]
            data['event_info'] = get_event_seatsio_info(data)
            chart_key = data["chart_key"]
            seats = get_object_statuses(data["event_key"], chart_key)
            self.tickets_info = group_tickets_by_type_and_status(seats, free_only=False)

            # Populate team combo
            self.team_combo.clear()
            if "home_team" in self.webook_data["data"] and self.webook_data["data"]["home_team"]:
                home_team = self.webook_data["data"]["home_team"]
                self.team_combo.addItem(home_team["name"], home_team["_id"])
            if "away_team" in self.webook_data["data"] and self.webook_data["data"]["away_team"]:
                away_team = self.webook_data["data"]["away_team"]
                self.team_combo.addItem(away_team["name"], away_team["_id"])

            self.seat_id_map.clear()
            for ttype, statuses in self.tickets_info.items():
                for status, seats in statuses.items():
                    for seat in seats:
                        seat_id = seat
                        self.seat_id_map[seat_id] = {
                            'type': ttype,
                            'status': status
                        }

            # Initialize core components
            self._initialize_websocket(data)
            self._update_initial_ui()
            self._start_fast_holding_system()

            # Initialize token management system
            if hasattr(self, 'token_system') and self.token_system:
                self.token_system.shutdown()

            self.token_system = TokenManagementSystem(
                event_key=self.webook_data['data']['seats_io']['event_key'],
                chart_key=self.webook_data['data']['seats_io']['chart_key'],
                channel_keys=self.webook_data['data']['channel_keys'],
                team_id=self.team_combo.currentData(),
                proxies=[self.get_proxy_string()] if self.get_proxy_string() else []
            )

            # Connect signals
            self.token_system.log_signal.connect(self.log)
            self.token_system.seat_held_signal.connect(self.on_seat_held)
            self.token_system.seat_released_signal.connect(self.on_seat_released)
            self.token_system.seat_transferred_signal.connect(self.on_seat_transferred)
            self.token_system.seat_expired_signal.connect(self.on_seats_expired)
            self.token_system.ui_update_signal.connect(self.held_seats_tab.auto_refresh_held_seats)
            self.token_system.token_renewed_signal.connect(self.on_token_renewed)
            self.initialize_channel_key_refresh_timer()
            # Log the cached event ID for debugging
            event_id = get_cached_event_id()
            if event_id:
                self.log(f"Event ID cached: {event_id}")
            else:
                self.log("Warning: Event ID could not be cached")

        except Exception as e:
            logger.exception("Failed to load event info", exc_info=True)
            QMessageBox.critical(self, "Error", f"Failed to load event info:\n{str(e)}")

    def on_websocket_connected(self, connected):
        """Handle WebSocket connection status changes"""
        if connected:
            self.log("[WebSocket] Connected to event updates.")
        else:
            self.log("[WebSocket] Disconnected from event updates.")

    def on_token_renewed(self, token_id, time_left):
        """Handle token renewed signal"""
        self.log(f"Token {token_id} renewed with {time_left} seconds remaining")
        # Force UI refresh with rebuild
        self.held_seats_tab.auto_refresh_held_seats(force_rebuild=True)

    def on_performance_signal(self, stats_data):
        """
        Handle performance statistics from FastHoldManager.

        Args:
            stats_data: Dictionary containing performance metrics
                - avg_ms: Average response time in milliseconds
                - success: Boolean indicating if the operation was successful
                - seat_id: The seat ID that was processed
                - operation: The type of operation (e.g., 'hold', 'release')
        """
        try:
            # Create performance stats object if it doesn't exist
            if not hasattr(self, 'performance_stats'):
                from performance_testing import PerformanceStats
                self.performance_stats = PerformanceStats()

            # Extract data from the stats dictionary
            avg_ms = stats_data.get('avg_ms', 0)
            success = stats_data.get('success', False)
            seat_id = stats_data.get('seat_id', 'unknown')
            operation = stats_data.get('operation', 'unknown')

            # Record network time
            self.performance_stats.record_network_time(avg_ms)

            # If we have timing data for this seat in auto_held_seats, record completion
            start_time = None
            with self.data_lock:
                if seat_id in self.auto_held_seats and isinstance(self.auto_held_seats[seat_id], float):
                    start_time = self.auto_held_seats[seat_id]

            # Record completion if we have a start time
            if start_time:
                self.performance_stats.record_completion(seat_id, start_time, success)

            # Log performance data at debug level
            logger.debug(f"Performance data for {operation} on seat {seat_id}: {avg_ms:.2f}ms, success={success}")

        except Exception as e:
            logger.error(f"Error processing performance data: {str(e)}")

    def on_refresh_error(self, error_message):
        """Handle refresh errors from WebSocket or workers"""
        self.log(f"[WebSocket/Worker] Error: {error_message}")
        logger.error(f"[WebSocket/Worker] Error: {error_message}")

    def on_book_seats(self):
        """Handle book seats button click with non-blocking UI implementation"""
        selected_types = self.ticket_type_tab.get_selected_types()
        if not selected_types:
            self.log("No ticket types selected for booking.")
            return

        try:
            desired_total = int(self.total_tickets_edit.text())
            if desired_total <= 0:
                self.log("Desired number of tickets must be greater than zero.")
                return
        except ValueError:
            self.log("Please enter a valid number for total tickets.")
            return

        # Collect free seats by type
        free_seats_by_type = {}
        for ttype in selected_types:
            if ttype in self.tickets_info:
                free_seats_by_type[ttype] = list(self.tickets_info[ttype].get("free", {}).values())
            else:
                free_seats_by_type[ttype] = []

        total_free_seats = sum(len(seats) for seats in free_seats_by_type.values())
        if total_free_seats < desired_total:
            self.log(f"Not enough free seats available. Available: {total_free_seats}, Desired: {desired_total}")
            return

        proxy_str = self.get_proxy_string()

        # Get event_id if available
        event_id = None
        if hasattr(self, 'webook_data') and self.webook_data:
            event_id = self.webook_data.get("data", {}).get("_id")

        # Create the worker
        from worker.booking_worker import BookingWorker

        self.booking_worker = BookingWorker(
            event_key=self.webook_data['data']['seats_io']['event_key'],
            chart_key=self.webook_data['data']['seats_io']['chart_key'],
            channel_keys=self.webook_data['data']['channel_keys'],
            desired_count=desired_total,
            free_seats_by_type=free_seats_by_type,
            team_id=self.team_combo.currentData(),
            proxies=[proxy_str] if proxy_str else [],
            name="ManualBooker",
            event_id=event_id  # Pass event_id to BookingWorker
        )

        # Set parent to allow access to main_window
        self.booking_worker.parent = lambda: self

        # Connect signals
        self.booking_worker.log_signal.connect(self.log)
        self.booking_worker.finished_signal.connect(self.on_booking_finished)

        # Connect progress signals for logging
        self.booking_worker.progress_signals.started.connect(self.on_booking_started)
        self.booking_worker.progress_signals.progress.connect(self.on_booking_progress)
        self.booking_worker.progress_signals.seat_held.connect(self.on_seat_booked)
        self.booking_worker.progress_signals.token_created.connect(self.on_token_created)
        self.booking_worker.progress_signals.error.connect(self.on_booking_error)
        self.booking_worker.progress_signals.finished.connect(self.on_booking_complete)

        # Save current state of book button
        self.book_button_text = self.book_button.text()
        self.book_button.setText("Cancel Booking")
        self.book_button.clicked.disconnect()
        self.book_button.clicked.connect(self.cancel_booking)

        # Start the worker
        self.booking_worker.proxy_result_signal.connect(self.record_proxy_performance)
        self.booking_worker.start()
        self.log("🚀 Started booking process...")

    def on_booking_started(self, total):
        """Handle booking starting"""
        self.log(f"⏳ Beginning to book {total} seats...")

    def on_booking_progress(self, current, total):
        """Update booking progress in logs"""
        # Only log every 5th progress update to avoid log spam
        if current % 5 == 0 or current == total:
            self.log(f"⏳ Booking progress: {current}/{total} seats")

    def on_seat_booked(self, seat_id, token_id):
        """Handle seat booked event"""
        # No special UI needed here - already logged by the worker

    def on_token_created(self, token_id):
        """Handle token created event"""
        self.log(f"🔑 Created new token: {token_id} (max 5 seats per token)")

    def on_booking_error(self, error_message):
        """Handle booking error"""
        self.log(f"❌ Booking error: {error_message}")

    def on_booking_complete(self):
        """Handle booking completion"""
        self.log("✅ Booking process finished")
        # Restore book button
        if hasattr(self, 'book_button_text'):
            self.book_button.setText(self.book_button_text)
            self.book_button.clicked.disconnect()
            self.book_button.clicked.connect(self.on_book_seats)

    def cancel_booking(self):
        """Cancel the booking process"""
        if hasattr(self, 'booking_worker') and self.booking_worker.isRunning():
            self.log("🛑 Cancelling booking process...")
            self.booking_worker.stop()
            # Restore book button
            if hasattr(self, 'book_button_text'):
                self.book_button.setText(self.book_button_text)
                self.book_button.clicked.disconnect()
                self.book_button.clicked.connect(self.on_book_seats)

    def get_proxy_string(self):
        """Get a proxy string using the proxy manager for optimal selection"""
        # Use proxy manager to get the best proxy
        proxy_manager = get_global_proxy_manager()

        # If proxy is disabled or no proxies available, return None
        if not self.proxy_config.get("enabled", False):
            return None

        # Get the proxy based on the mode
        if self.proxy_config.get("mode", "single") == "single":
            # Single proxy mode - Format manually
            domain = self.proxy_config["domain"]
            port = self.proxy_config["port"]
            user = self.proxy_config["username"]
            pwd = self.proxy_config["password"]

            # Return in format appropriate for the system
            return f"{domain}:{port}:{user}:{pwd}"
        else:
            # List mode - Get best proxy from manager
            proxy_str = proxy_manager.get_proxy()
            return proxy_str


    def reload_event_data(self, event_key=None, force_reload=False):
        """
        Reload event data and update the application state.
        
        Args:
            event_key: Event key to reload, or None to use current event key
            force_reload: Whether to force a reload even if channel keys haven't changed
        
        Returns:
            bool: True if reload was successful, False otherwise
        """
        # Use current event key if none provided
        if not event_key and hasattr(self, 'webook_data') and self.webook_data:
            event_key = self.webook_data['data']['seats_io']['event_key']
        
        if not event_key:
            self.log("❌ No event key available for reload")
            return False
        
        self.log(f"🔄 Reloading event data for {event_key}")
        
        try:
            # Get latest event info
            webook_data = get_webook_event_info(event_key=event_key)
            
            if not webook_data.ok:
                self.log(f'❌ Failed to reload event data: {webook_data.reason}')
                return False
                
            new_data = webook_data.json()
            
            # Check if channel keys have changed (if not forcing reload)
            if not force_reload and hasattr(self, 'webook_data') and self.webook_data:
                old_keys = self.webook_data['data']['channel_keys']
                new_keys = new_data['data']['channel_keys']
                
                old_keys_json = json.dumps(old_keys, sort_keys=True)
                new_keys_json = json.dumps(new_keys, sort_keys=True)
                
                if old_keys_json == new_keys_json:
                    self.log("✓ Channel keys are unchanged, skipping reload")
                    return True
                else:
                    self.log("🔄 Channel keys have changed, updating data")
            
            # Update webook data
            self.webook_data = new_data
            
            # Process data
            data = self.webook_data["data"]["seats_io"]
            data['event_info'] = get_event_seatsio_info(data)
            
            # Cache the event ID for token creation
            cache_event_id(self.webook_data)
            
            # Reload seats data
            chart_key = data["chart_key"]
            seats = get_object_statuses(data["event_key"], chart_key)
            self.tickets_info = group_tickets_by_type_and_status(seats, free_only=False)
            
            # Rebuild seat map
            self.seat_id_map.clear()
            for ttype, statuses in self.tickets_info.items():
                for status, seats in statuses.items():
                    for seat in seats:
                        seat_id = seat
                        self.seat_id_map[seat_id] = {
                            'type': ttype,
                            'status': status
                        }
            
            # Update subsystems with new channel keys
            if hasattr(self, 'token_system') and self.token_system:
                self.token_system.channel_keys = self.webook_data['data']['channel_keys']
                
            if hasattr(self, 'fast_hold_manager') and self.fast_hold_manager:
                self.fast_hold_manager.channel_keys = self.webook_data['data']['channel_keys']
            
            # Reload WebSocket if needed
            if hasattr(self, 'websocket_manager') and self.websocket_manager:
                # Stop current websocket
                self.websocket_manager.stop()
                # Initialize with fresh data
                self._initialize_websocket(data)
            
            # Update UI
            self._update_initial_ui()
            
            self.log("✅ Event data reloaded successfully")
            return True
            
        except Exception as e:
            self.log(f"❌ Error reloading event data: {str(e)}")
            logger.error(f"Error reloading event data: {str(e)}", exc_info=True)
            return False
        
    def on_booking_finished(self, result):
        """Handle completion of booking worker with booking statistics"""
        # Clean up the worker thread
        sender_worker = self.sender()
        if isinstance(sender_worker, QThread):
            sender_worker.quit()
            sender_worker.wait()
            sender_worker.deleteLater()

        if result and result.get("success", False):
            booked_count = result.get("booked_count", 0)
            token_count = result.get("token_count", 0)
            booked_seats = result.get("booked_seats", {})

            self.log(f"✅ Booked {booked_count} seats with {token_count} tokens")

            # Register all booked seats with the token system
            if self.token_system:
                seat_count = 0
                for token_id, seat_ids in booked_seats.items():
                    for seat_id in seat_ids:
                        self.token_system.register_held_seat(seat_id, token_id)
                        seat_count += 1

                self.log(f"📦 Added {seat_count} seats to token management system")
            else:
                self.log(f"⚠️ Token system not initialized, seats won't be managed automatically")
        else:
            error = result.get("error", "Unknown error") if result else "Unknown error"
            self.log(f"❌ Booking failed: {error}")

        # Restore book button
        if hasattr(self, 'book_button_text'):
            self.book_button.setText(self.book_button_text)
            self.book_button.clicked.disconnect()
            self.book_button.clicked.connect(self.on_book_seats)

        # Refresh seats UI
        self.held_seats_tab.auto_refresh_held_seats()


    def on_release_seats(self):
        """Handle release seats button click"""
        if self.token_system:
            # Get all seats
            all_seats = self.token_system.get_all_seats()
            if not all_seats:
                self.log("[Main] No seats to release.")
                return

            # Confirm with user
            msg = QMessageBox.question(
                self,
                "Confirm Release",
                f"Are you sure you want to release all {len(all_seats)} seats?",
                QMessageBox.Yes | QMessageBox.No
            )

            if msg != QMessageBox.Yes:
                return

            # Release all seats
            released_count = 0
            for seat_id in list(all_seats.keys()):
                if self.token_system.release_seat(seat_id):
                    released_count += 1

            self.log(f"Released {released_count}/{len(all_seats)} seats")
            self.held_seats_tab.auto_refresh_held_seats(force_rebuild=True)
            return

        # No token system initialized
        self.log("[Main] Token system not initialized. Cannot release seats.")


    def get_now(self) -> float:
        """Helper so we can easily mock time if needed."""
        return time.time()

    def closeEvent(self, event: QEvent):
        """Clean up resources when window is closed"""
        # Stop threads
        if self.websocket_manager:
            self.websocket_manager.stop()
            self.websocket_manager = None

        if hasattr(self, 'time_left_updater') and self.time_left_updater and self.time_left_updater.isRunning():
            self.time_left_updater.stop()
            self.time_left_updater.wait()


        if hasattr(self, 'token_system') and self.token_system:
            self.token_system.shutdown()

        # Cleanly shut down token renewal timer
        if hasattr(self, 'renewal_timer') and self.renewal_timer.isActive():
            self.renewal_timer.stop()
            
        # Stop channel key refresh timer
        if hasattr(self, 'channel_key_timer') and self.channel_key_timer.isActive():
            self.channel_key_timer.stop()

        # Let parent handle the rest
        super().closeEvent(event)
