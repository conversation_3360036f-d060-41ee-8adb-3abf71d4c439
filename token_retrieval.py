# Enhanced token_retrieval.py with account manager integration

import requests
import logging
import json
import time
import threading
import random
from functools import lru_cache

# Import the account token manager
from account_token_manager import get_account_token, report_invalid_token

logger = logging.getLogger('webook_pro')

# Event ID cache
_event_id_cache = {}
_event_id_lock = threading.RLock()

def get_hold_token(event_id=None, proxy=None, account_token=None):
    """
    Get a hold token from the Webook API using http.client.
    
    Args:
        event_id: The event ID to get a hold token for (or None to use cached)
        proxy: Optional proxy to use
        account_token: Optional account token to use (otherwise will get one automatically)
    
    Returns:
        A hold token string or None if failed
    """
    import http.client
    import json
    import socket
    import logging
    
    logger = logging.getLogger('webook_pro')
    
    # Get event ID from cache if not provided
    if not event_id:
        event_id = get_cached_event_id()
        if not event_id:
            logger.error("No event ID available for hold token request")
            return None
    
    # Get account token if not provided
    if not account_token:
        account_token = get_account_token()
        if not account_token:
            logger.error("No account token available for hold token request")
            return None
    
    # Prepare headers
    headers = {
        'accept': 'application/json',
        'accept-language': 'en-US,en;q=0.9',
        'authorization': f'Bearer {account_token}',
        'cache-control': 'no-cache',
        'content-type': 'application/json',
        'origin': 'https://webook.com',
        'pragma': 'no-cache',
        'token': 'e9aac1f2f0b6c07d6be070ed14829de684264278359148d6a582ca65a50934d2',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    }
    
    # Prepare payload
    payload = {
        'event_id': event_id,
        'lang': 'en'
    }
    
    try:
        # Handle proxy if provided
        print(proxy)
        if proxy:
            try:
                # If socks proxy is specified, we need to use PySocks
                import socks
                
                parts = proxy.split(":")
                if len(parts) == 4:
                    proxy_host, proxy_port, proxy_user, proxy_pass = parts
                    socks.set_default_proxy(
                        socks.HTTP, 
                        proxy_host, 
                        int(proxy_port), 
                        username=proxy_user, 
                        password=proxy_pass
                    )
                elif len(parts) == 2:
                    proxy_host, proxy_port = parts
                    socks.set_default_proxy(socks.HTTP, proxy_host, int(proxy_port))
                
                # Apply proxy to the socket module
                socket.socket = socks.socksocket
                logger.info(f"Using proxy: {proxy_host}:{proxy_port}")
            except ImportError:
                logger.error("PySocks module not available, can't use SOCKS proxy")
                return None
        
        # Create connection
        conn = http.client.HTTPSConnection('api.webook.com', timeout=5)
        
        # Convert payload to JSON
        payload_json = json.dumps(payload)
        
        # Send request
        conn.request(
            'POST', 
            '/api/v2/seats/hold-token', 
            body=payload_json,
            headers=headers
        )
        
        # Get response
        response = conn.getresponse()
        status_code = response.status
        
        if status_code == 401 or status_code == 403:
            # Authentication failure - report invalid token
            logger.warning(f"Authentication failure with token - reporting as invalid")
            report_invalid_token(account_token)
            
            # Try once more with a fresh token
            new_token = get_account_token()
            if new_token and new_token != account_token:
                conn.close()
                return get_hold_token(event_id, proxy, new_token)
            conn.close()
            return None
            
        if status_code != 200:
            response_text = response.read().decode('utf-8')
            logger.error(f"Error getting hold token: Status {status_code} - {response_text}")
            conn.close()
            return None
        
        # Read and parse response
        response_data = response.read().decode('utf-8')
        data = json.loads(response_data)
        
        if not data.get('status') == 'success':
            logger.error(f"Error in hold token response: {data}")
            conn.close()
            return None
        
        hold_token = data.get('data', {}).get('token')
        if not hold_token:
            logger.error(f"No hold token in response: {data}")
            conn.close()
            return None
        
        logger.info(f"Successfully got hold token: {hold_token[:8]}...")
        conn.close()
        return hold_token
        
    except Exception as e:
        logger.error(f"Error getting hold token: {str(e)}")
        return None
    finally:
        # Reset socket if we modified it for proxy
        if proxy:
            try:
                # Reset socket to default if we modified it
                socket.socket = socket._real_socket
            except:
                pass

def cache_event_id(event_data):
    """
    Extract and cache the event ID from event data.
    
    Args:
        event_data: Event data response from API
    
    Returns:
        The extracted event ID
    """
    if not event_data or not isinstance(event_data, dict):
        return None
    
    event_id = event_data.get("data", {}).get("_id")
    if event_id:
        # Store in cache for future use
        _set_cached_event_id(event_id)
        return event_id
    
    return None


def _set_cached_event_id(event_id, event_key=None):
    """Store event ID in cache"""
    with _event_id_lock:
        _event_id_cache["default"] = event_id
        if event_key:
            _event_id_cache[event_key] = event_id


def get_cached_event_id(event_key=None):
    """Get cached event ID"""
    with _event_id_lock:
        if event_key and event_key in _event_id_cache:
            return _event_id_cache[event_key]
        return _event_id_cache.get("default")


# Batch token creation functionality

def create_hold_tokens_batch(count, event_id=None, proxy=None):
    """
    Create multiple hold tokens in parallel.
    
    Args:
        count: Number of tokens to create
        event_id: Event ID (or None to use cached)
        proxy: Optional proxy to use
    
    Returns:
        List of created tokens
    """
    if not event_id:
        event_id = get_cached_event_id()
        if not event_id:
            logger.error("No event ID available for batch token creation")
            return []
    
    # Create tokens in parallel using multiple threads
    tokens = []
    from concurrent.futures import ThreadPoolExecutor
    
    def create_single_token():
        return get_hold_token(event_id, proxy)
    
    with ThreadPoolExecutor(max_workers=min(count, 5)) as executor:
        future_to_token = {executor.submit(create_single_token): i for i in range(count)}
        for future in future_to_token:
            try:
                token = future.result()
                if token:
                    tokens.append(token)
            except Exception as e:
                logger.error(f"Error creating token in batch: {str(e)}")
    
    logger.info(f"Created {len(tokens)} tokens in batch (requested {count})")
    return tokens