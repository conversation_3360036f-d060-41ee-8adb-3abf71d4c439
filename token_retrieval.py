# Enhanced token_retrieval.py with account manager integration and improved proxy handling

import requests
import logging
import json
import time
import threading
import random
from functools import lru_cache
import httpx

# Import the account token manager
from account_token_manager import get_account_token, report_invalid_token

logger = logging.getLogger('webook_pro')

# Event ID cache
_event_id_cache = {}
_event_id_lock = threading.RLock()

def get_hold_token(event_id=None, proxy=None, account_token=None):
    """
    Get a hold token from the Webook API using httpx.
    Args:
        event_id: The event ID to get a hold token for (or None to use cached)
        proxy: SOCKS proxy to use (formats: "user:password@host:port" or "host:port")
        account_token: Optional account token to use

    Returns:
        A hold token string or None if failed
    """
    try:
        import httpx
    except ImportError:
        print("httpx is not installed. Please run 'pip install httpx[socks]' to use this function.")
        return None

    import logging

    logger = logging.getLogger('webook_pro')
    url = 'https://api.webook.com/api/v2/seats/hold-token'

    try:
        # Get event ID from cache if not provided
        if not event_id:
            # Assuming get_cached_event_id is defined elsewhere
            event_id = get_cached_event_id()
            if not event_id:
                logger.error("No event ID available for hold token request")
                return None

        # Get account token if not provided
        if not account_token:
            # Assuming get_account_token is defined elsewhere
            account_token = get_account_token()
            if not account_token:
                logger.error("No account token available for hold token request")
                return None

        headers = {
            'accept': 'application/json',
            'accept-language': 'en-US,en;q=0.9',
            'authorization': f'Bearer {account_token}',
            'cache-control': 'no-cache',
            'content-type': 'application/json',
            'origin': 'https://webook.com',
            'pragma': 'no-cache',
            'token': 'e9aac1f2f0b6c07d6be070ed14829de684264278359148d6a582ca65a50934d2',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/******** Firefox/121.0'
        }

        payload = {
            'event_id': event_id,
            'lang': 'en'
        }

        # httpx uses a dictionary of proxy URLs. We assume SOCKS5.
        # proxies = {
        #     'all://': f'socks5://{proxy}'
        # } if proxy else None
        
        # if proxies:
        #     logger.info(f"Using SOCKS5 proxy with httpx: {proxy}")
        # httpx now uses proxy not proxies
        # format proxy
        if proxy:
            parts = proxy.split(":")
            if len(parts) == 4:
                proxy_host, proxy_port, proxy_user, proxy_pass = parts
                proxy = f"socks5h://{proxy_user}:{proxy_pass}@{proxy_host}:{proxy_port}"
            elif len(parts) == 2:
                proxy_host, proxy_port = parts
                proxy = f"socks5h://{proxy_host}:{proxy_port}"
            else:
                proxy = f"socks5h://{proxy}"
        # httpx client is used within a context manager for proper resource handling
        with httpx.Client(proxy=proxy, timeout=5.0, verify=False) as client:
            response = client.post(url, json=payload, headers=headers)
            
            status_code = response.status_code

            if status_code in [401, 403]:
                logger.warning(f"Authentication failure with token - reporting as invalid")
                # Assuming report_invalid_token is defined elsewhere
                report_invalid_token(account_token)
                
                # Assuming get_account_token is defined
                new_token = get_account_token()
                if new_token and new_token != account_token:
                    # Recursively call with the new token
                    return get_hold_token(event_id, proxy, new_token)
                return None

            # Will raise an httpx.HTTPStatusError for 4xx/5xx responses if we use response.raise_for_status()
            # Or we can check manually like this:
            if status_code != 200:
                logger.error(f"Error getting hold token: Status {status_code} - {response.text}")
                return None

            data = response.json()

            if not data.get('status') == 'success':
                logger.error(f"Error in hold token response: {data}")
                return None

            hold_token = data.get('data', {}).get('token')
            if not hold_token:
                logger.error(f"No hold token in response: {data}")
                return None

            logger.info(f"Successfully got hold token: {hold_token[:8]}...")
            return hold_token

    # Catching specific httpx exceptions is more robust
    except httpx.ProxyError as e:
        logger.error(f"SOCKS Proxy Error: Please check your proxy address and credentials. Details: {e}")
        return None
    except httpx.ConnectTimeout as e:
        logger.error(f"Connection Timed Out: The server (or proxy) failed to respond in time. Details: {e}")
        return None
    except httpx.ConnectError as e:
        logger.error(f"Connection Error: Failed to establish a connection. Details: {e}")
        return None
    except Exception as e:
        logger.error(f"An unexpected error occurred: {e.__class__.__name__} - {str(e)}")
        return None

def cache_event_id(event_data):
    """
    Extract and cache the event ID from event data.
    
    Args:
        event_data: Event data response from API
    
    Returns:
        The extracted event ID
    """
    if not event_data or not isinstance(event_data, dict):
        return None
    
    event_id = event_data.get("data", {}).get("_id")
    if event_id:
        # Store in cache for future use
        _set_cached_event_id(event_id)
        return event_id
    
    return None


def _set_cached_event_id(event_id, event_key=None):
    """Store event ID in cache"""
    with _event_id_lock:
        _event_id_cache["default"] = event_id
        if event_key:
            _event_id_cache[event_key] = event_id


def get_cached_event_id(event_key=None):
    """Get cached event ID"""
    with _event_id_lock:
        if event_key and event_key in _event_id_cache:
            return _event_id_cache[event_key]
        return _event_id_cache.get("default")


# Batch token creation functionality

def create_hold_tokens_batch(count, event_id=None, proxy=None):
    """
    Create multiple hold tokens in parallel.
    
    Args:
        count: Number of tokens to create
        event_id: Event ID (or None to use cached)
        proxy: Optional proxy to use
    
    Returns:
        List of created tokens
    """
    if not event_id:
        event_id = get_cached_event_id()
        if not event_id:
            logger.error("No event ID available for batch token creation")
            return []
    
    # Create tokens in parallel using multiple threads
    tokens = []
    from concurrent.futures import ThreadPoolExecutor
    
    def create_single_token():
        return get_hold_token(event_id, proxy)
    
    with ThreadPoolExecutor(max_workers=min(count, 5)) as executor:
        future_to_token = {executor.submit(create_single_token): i for i in range(count)}
        for future in future_to_token:
            try:
                token = future.result()
                if token:
                    tokens.append(token)
            except Exception as e:
                logger.error(f"Error creating token in batch: {str(e)}")
    
    logger.info(f"Created {len(tokens)} tokens in batch (requested {count})")
    return tokens
